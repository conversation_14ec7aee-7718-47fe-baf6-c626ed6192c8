﻿package com.cet.eem.fusion.maintenance.core.model.devicemanage;

import com.cet.eem.fusion.maintenance.core.model.devicemanage.template.MeasureNodeDto;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.template.MeasureNodeGroup;
import com.cet.eem.common.definition.ModelLabelDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName : MeasureNodeGroupWithLayer
 * @Description : 测点分组和测点信息
 * <AUTHOR> jiangzixuan
 * @Date: 2021-06-15 18:56
 */
@Data
public class MeasureNodeGroupWithLayer extends MeasureNodeGroup {
    @JsonProperty(ModelLabelDef.MEASURE_NODE + "_model")
    private List<MeasureNodeDto> list;
}
