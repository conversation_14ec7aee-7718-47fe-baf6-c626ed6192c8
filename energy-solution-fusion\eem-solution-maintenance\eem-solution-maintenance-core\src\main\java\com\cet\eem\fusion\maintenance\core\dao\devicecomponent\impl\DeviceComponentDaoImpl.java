﻿package com.cet.eem.fusion.maintenance.core.dao.devicecomponent.impl;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.DeviceComponent;
import com.cet.eem.fusion.maintenance.core.dao.devicecomponent.DeviceComponentDao;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName : ComponentDaoImpl
 * @Description : 零件相关
 * <AUTHOR> jzx
 * @Date: 2021-05-17 13:44
 */
@Repository
public class DeviceComponentDaoImpl extends ModelDaoImpl<DeviceComponent> implements DeviceComponentDao {

    @Override
    public List<DeviceComponent> queryComponentByDevice(String objectLabel, Long objectId) {
        LambdaQueryWrapper<DeviceComponent> queryWrapper = LambdaQueryWrapper.of(DeviceComponent.class)
                .eq(DeviceComponent::getObjectId, objectId)
                .eq(DeviceComponent::getObjectLabel, objectLabel);
        return this.selectList(queryWrapper);
    }

    @Override
    public DeviceComponent querySingle(String objectLabel, Long objectId, String name, String model) {

        LambdaQueryWrapper<DeviceComponent> wrapper = LambdaQueryWrapper.of(DeviceComponent.class)
                .eq(DeviceComponent::getModel, model)
                .eq(DeviceComponent::getObjectId, objectId)
                .eq(DeviceComponent::getObjectLabel, objectLabel);
        return this.selectOne(wrapper);

    }

    @Override
    public DeviceComponent queryWhileEdit(String objectLabel, Long objectId, String name, Long id, String model) {

        LambdaQueryWrapper<DeviceComponent> wrapper = LambdaQueryWrapper.of(DeviceComponent.class)
                .eq(DeviceComponent::getModel, model)
                .eq(DeviceComponent::getObjectId, objectId)
                .eq(DeviceComponent::getObjectLabel, objectLabel)
                .ne(DeviceComponent::getId, id);
        return this.selectOne(wrapper);

    }

    @Override
    public List<DeviceComponent> queryList(List<String> objectLabels, List<Long> objectIds, String name) {
        LambdaQueryWrapper<DeviceComponent> queryWrapper = LambdaQueryWrapper.of(DeviceComponent.class)
                .eq(DeviceComponent::getName, name)
                .in(DeviceComponent::getObjectId, objectIds)
                .in(DeviceComponent::getObjectLabel, objectLabels);
        return this.selectList(queryWrapper);
    }

    @Override
    public List<DeviceComponent> queryByProjectId(Long projectId) {
        LambdaQueryWrapper<DeviceComponent> wrapper = LambdaQueryWrapper.of(DeviceComponent.class)
                .eq(DeviceComponent::getProjectId, projectId);
        return this.selectList(wrapper);
    }

    @Override
    public List<DeviceComponent> queryComponentByDevice(List<BaseVo> baseNode) {
        if (CollectionUtils.isEmpty(baseNode)) {
            return Collections.emptyList();
        }

        Map<String, List<BaseVo>> deviceMap = baseNode.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        LambdaQueryWrapper<DeviceComponent> wrapper = LambdaQueryWrapper.of(DeviceComponent.class);
        //按modellabel分类，进行查询id和label对应的零件信息
        deviceMap.forEach((key, val) -> {
            Set<Long> ids = val.stream().map(BaseVo::getId).collect(Collectors.toSet());
            wrapper.or(it -> it.eq(DeviceComponent::getObjectLabel, key).in(DeviceComponent::getObjectId, ids));
        });
        return selectList(wrapper);
    }
}

