﻿package com.cet.eem.fusion.maintenance.core.model.workorder.maintenance;

import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.fusion.common.model.Page;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @ClassName : QueryMaintenanceWorkOrderRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-26 09:05
 */
@Getter
@Setter
@ApiModel(description = "查询维保工单")
public class QueryMaintenanceWorkOrderRequest {

    @ApiModelProperty("开始时间")
    @NotNull(message = "开始时间不允许为空！")
    private Long startTime;

    @ApiModelProperty("结束时间")
    @NotNull(message = "结束时间不允许为空！")
    private Long endTime;

    @ApiModelProperty("班组")
    @JsonProperty("teamid")
    private Long teamId;

    @ApiModelProperty("工单号，模糊匹配")
    private String workOrder;

    @ApiModelProperty("工单状态")
    private Integer workSheetStatus;

    @ApiModelProperty("等级")
    @JsonProperty(WorkOrderDef.TASK_LEVEL)
    private Integer taskLevel;

    @ApiModelProperty("分页参数")
    private Page page;
    @ApiModelProperty("租户id")
    private Long tenantId;
}

