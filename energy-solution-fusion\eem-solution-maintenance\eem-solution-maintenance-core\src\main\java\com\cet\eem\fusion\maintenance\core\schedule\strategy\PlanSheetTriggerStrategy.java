﻿package com.cet.eem.fusion.maintenance.core.schedule.strategy;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;
import org.quartz.ScheduleBuilder;
import org.quartz.Trigger;

/**
 * <AUTHOR>
 */
public interface PlanSheetTriggerStrategy<T extends Trigger> {

    /**
     * 根据巡检计划生成周期
     *
     * @param planSheet
     * @return
     */
    ScheduleBuilder<T> buildSchedule(PlanSheet planSheet);
}

