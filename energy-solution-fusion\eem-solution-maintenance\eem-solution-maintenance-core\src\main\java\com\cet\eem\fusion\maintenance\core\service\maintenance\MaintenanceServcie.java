﻿package com.cet.eem.fusion.maintenance.core.service.maintenance;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.MaintenanceGroup;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.MaintenanceItem;
import com.cet.eem.fusion.maintenance.core.model.maintance.item.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

public interface MaintenanceServcie {

    /**
     * 查询项目下所有的维保项目组
     *
     * @return
     */
    List<MaintenanceGroup> queryAllGroupInThisProject();

    /**
     * 新增维保项目组
     *
     * @param addMaintenanceGroupRequest
     * @return
     */
    MaintenanceGroup addMaintenanceGroup(AddMaintenanceGroupRequest addMaintenanceGroupRequest);

    /**
     * 编辑维保项目组
     *
     * @param editMaintenanceGroupRequest
     * @return
     */
    MaintenanceGroup editMaintenanceGroup(EditMaintenanceGroupRequest editMaintenanceGroupRequest);

    /**
     * 删除维保项目组
     *
     * @param ids
     */
    void deleteMaintenanceGroup(Collection<Long> ids);


    /**
     * 查询所有维保项
     *
     * @param maintenanceGroupId
     * @return
     */
    List<MaintenanceItemVo> queryAllMaintenanceItem(Long maintenanceGroupId);

    /**
     * 查询维保计划中的维保项
     *
     * @param planSheetId
     * @return
     */
    List<MaintenanceItemVo> queryMaintenanceItemByPlanSheetId(Long planSheetId);

    /**
     * 查询维保计划中的维保项
     *
     * @param searchVo
     * @return
     */
    List<MaintenanceItemVo> queryMaintenanceItemByPlanSheetId(MaintenanceItemSearchVo searchVo);

    /**
     * 添加维保项
     *
     * @param addMaintenanceItemRequest
     * @return
     */
    MaintenanceItem addMaintenanceItem(AddMaintenanceItemRequest addMaintenanceItemRequest);

    /**
     * 编辑维保项
     *
     * @param editMaintenanceItemRequest
     * @return
     */
    MaintenanceItem editMaintenanceItem(EditMaintenanceItemRequest editMaintenanceItemRequest);

    /**
     * 编辑维保项顺序
     *
     * @param maintenanceItemSortVos
     */
    void editMaintenanceItemSort(List<MaintenanceItemSortVo> maintenanceItemSortVos);

    /**
     * 删除维保项
     *
     * @param ids
     */
    void deleteMaintenanceItem(List<Long> ids);

    /**
     * 导出维保项
     *
     * @param ids
     */
    void exportItem(HttpServletResponse response, List<Long> ids);

    /**
     * 导入维保项
     *
     * @param file
     */
    void importItem(MultipartFile file,Long projectId) throws IOException;
}

