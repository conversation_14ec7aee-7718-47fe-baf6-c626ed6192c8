﻿package com.cet.eem.fusion.maintenance.core.model.inspector;

import com.cet.eem.common.model.auth.user.RoleVo;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @ClassName : EditInspectorUserRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-22 14:03
 */
@Getter
@Setter
@ApiModel(value = "EditInspectorUserRequest", description = "巡检人员编辑")
public class EditInspectorUserRequest {
    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    private Long id;

    /**
     * 用户名
     */
    @NotEmpty(message = "用户名不能为空")
    private String name;

    /**
     * 用户角色信息
     */
    @NotNull(message = "用户角色信息不能为空")
    private RoleVo role;
}

