﻿package com.cet.eem.fusion.maintenance.core.dao.devicecomponent;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SpareParts;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SparePartsDevice;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.DeviceSystemWithSubLayer;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.DeviceWithSubLayer;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.component.DeviceImportList;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface DeviceDao extends BaseModelDao<SparePartsDevice> {
    List<DeviceSystemWithSubLayer> queryDeviceBySystem();

    List<SpareParts> querySparepartsByDeviceId(Long id);

    DeviceSystemWithSubLayer queryByNameAndId(String name, Long id, Long systemId);

    DeviceSystemWithSubLayer queryByModelAndObjectLabel(String model, String objectLabel, Long id, Long systemId);

    List<SpareParts> querySparepartsStorageByDevice(String model, String objectLabel);

    /**
     * 根据设备类型和型号筛选备件模型信息
     *
     * @param modelNodes 设备类型和信号信息
     * @return 备件模型信息
     */
    List<SparePartsDevice> queryByModelInfo(Set<DeviceImportList> modelNodes);

    List<DeviceWithSubLayer> queryByModelInfo(List<Long> sparePartsDeviceIdList, Collection<String> modelList);
}


