﻿package com.cet.eem.fusion.maintenance.core.model.devicemanage.template;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-04-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ModelLabel(ModelLabelDef.TECH_PARAM_TEMPLATE)
public class TechParam extends EntityWithName {

    private String name;

    private String unit;




}


