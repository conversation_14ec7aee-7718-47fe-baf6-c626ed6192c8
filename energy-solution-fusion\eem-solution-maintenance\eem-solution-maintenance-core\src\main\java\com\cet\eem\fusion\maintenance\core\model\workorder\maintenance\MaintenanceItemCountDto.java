﻿package com.cet.eem.fusion.maintenance.core.model.workorder.maintenance;

import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionWorkOrderDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

/**
 * @ClassName : MaintenanceItemCountDto
 * @Description : 维保项分类
 * <AUTHOR> jiangzixuan
 * @Date: 2021-06-11 09:15
 */

@Getter
@Setter
public class MaintenanceItemCountDto {

    private Integer maintenanceType;

    private String maintenanceTypeText;


    private MaintenanceItemContent maintenanceItemContents;


}
