﻿package com.cet.eem.fusion.maintenance.core.dao.handover.impl;

import com.cet.eem.fusion.maintenance.core.dao.handover.HandOverDao;
import com.cet.eem.fusion.maintenance.core.def.DutyStatusDef;
import com.cet.eem.fusion.maintenance.core.model.handover.ShiftingDutyPo;
import com.cet.eem.fusion.maintenance.core.model.handover.ShiftingDutySearchDto;
import com.cet.eem.fusion.common.utils.ParamUtils;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/3/23
 */
@Service
public class HandOverDaoImpl extends ModelDaoImpl<ShiftingDutyPo> implements HandOverDao {
    @Override
    @SuppressWarnings({"unchecked", "varargs"})
    public ApiResult<List<Map<String, Object>>> queryShiftingDuty(ShiftingDutySearchDto dto) {
        LambdaQueryWrapper<ShiftingDutyPo> wrapper = LambdaQueryWrapper.of(ShiftingDutyPo.class)
                .ge(ShiftingDutyPo::getStartTime, dto.getStartTime())
                .lt(ShiftingDutyPo::getStartTime, dto.getEndTime())
                .eq(ShiftingDutyPo::getProjectId, dto.getProjectId())
                .orderByDesc(ShiftingDutyPo::getStartTime);

        if (ParamUtils.checkPrimaryKeyValid(dto.getTeamId())) {
            wrapper.eq(ShiftingDutyPo::getTeamId, dto.getTeamId());
        }

        return this.selectMapsPage(wrapper, dto.getPage());
    }

    @Override
    @SuppressWarnings({"unchecked", "varargs"})
    public ShiftingDutyPo getLastOnDutyRecord(Long teamId, Long projectId, Long userId) {
        LambdaQueryWrapper<ShiftingDutyPo> wrapper = LambdaQueryWrapper.of(ShiftingDutyPo.class)
                .eq(ShiftingDutyPo::getDutyStatus, DutyStatusDef.ON_DUTY)
                .eq(ShiftingDutyPo::getTeamId, teamId)
                .eq(ShiftingDutyPo::getProjectId, projectId)
                .eq(ShiftingDutyPo::getDutyOfficer, userId)
                .orderByDesc(ShiftingDutyPo::getStartTime);

        Page page = new Page(0, 1);
        ApiResult<List<ShiftingDutyPo>> tmpResult = this.selectPage(wrapper, page);
        List<ShiftingDutyPo> data = tmpResult.getData();
        if (CollectionUtils.isNotEmpty(data)) {
            return data.get(0);
        }

        return null;
    }

    @Override
    @SuppressWarnings({"unchecked", "varargs"})
    public ShiftingDutyPo getHandoverPo(Long teamId, Long projectId) {
        LambdaQueryWrapper<ShiftingDutyPo> wrapper = LambdaQueryWrapper.of(ShiftingDutyPo.class)
                .eq(ShiftingDutyPo::getDutyStatus, DutyStatusDef.ON_DUTY)
                .eq(ShiftingDutyPo::getTeamId, teamId)
                .eq(ShiftingDutyPo::getProjectId, projectId)
                .orderByDesc(ShiftingDutyPo::getStartTime);

        Page page = new Page(0, 1);
        ApiResult<List<ShiftingDutyPo>> tmpResult = this.selectPage(wrapper, page);
        return getFirstItem(tmpResult);
    }

    @Override
    @SuppressWarnings({"unchecked", "varargs"})
    public <T extends ShiftingDutyPo> T queryLastShiftDuty(Long teamId, Integer dutyStatus, Long projectId, Long userId, Class<T> clazz) {
        LambdaQueryWrapper<ShiftingDutyPo> wrapper = LambdaQueryWrapper.of(ShiftingDutyPo.class)
                .eq(ShiftingDutyPo::getTeamId, teamId)
                .eq(ShiftingDutyPo::getProjectId, projectId)
                .orderByDesc(ShiftingDutyPo::getStartTime);

        if (ParamUtils.checkPrimaryKeyValid(dutyStatus)) {
            wrapper.eq(ShiftingDutyPo::getDutyStatus, dutyStatus);
        }

        if (ParamUtils.checkPrimaryKeyValid(userId)) {
            wrapper.eq(ShiftingDutyPo::getDutyOfficer, userId);
        }

        Page page = new Page(0, 1);
        ApiResult<List<Map<String, Object>>> tmpResult = this.selectMapsPage(wrapper, page);
        List<T> dataList = JsonTransferUtils.transferList(tmpResult.getData(), clazz);
        if (CollectionUtils.isNotEmpty(dataList)) {
            return dataList.get(0);
        }

        return null;
    }

    private ShiftingDutyPo getFirstItem(ApiResult<List<ShiftingDutyPo>> tmpResult) {
        List<ShiftingDutyPo> dataList = tmpResult.getData();
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }

        return dataList.get(0);
    }

    @Override
    @SuppressWarnings({"unchecked", "varargs"})
    public ShiftingDutyPo queryLastShiftDuty(Long teamId, Long projectId) {
        LambdaQueryWrapper<ShiftingDutyPo> wrapper = LambdaQueryWrapper.of(ShiftingDutyPo.class)
                .eq(ShiftingDutyPo::getTeamId, teamId)
                .eq(ShiftingDutyPo::getProjectId, projectId)
                .orderByDesc(ShiftingDutyPo::getEndTime);

        Page page = new Page(0, 1);
        ApiResult<List<ShiftingDutyPo>> tmpResult = this.selectPage(wrapper, page);
        return getFirstItem(tmpResult);
    }
}


