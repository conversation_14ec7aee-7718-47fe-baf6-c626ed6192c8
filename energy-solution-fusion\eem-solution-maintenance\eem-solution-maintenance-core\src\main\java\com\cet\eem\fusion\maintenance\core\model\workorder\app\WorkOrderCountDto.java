﻿package com.cet.eem.fusion.maintenance.core.model.workorder.app;

import com.cet.eem.fusion.maintenance.core.model.workorder.WoStatusCountDTO;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionWorkOrderDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/20
 */
@ApiModel(description = "工单计数")
@Getter
@Setter
public class WorkOrderCountDto extends WoStatusCountDTO {
    @ApiModelProperty("工单详情")
    private List<InspectionWorkOrderDto> workOrders;

    public WorkOrderCountDto(Integer workOrderStatus) {
        this.workOrderStatus = workOrderStatus;
        this.workOrders = Collections.singletonList(new InspectionWorkOrderDto());
    }

    public WorkOrderCountDto() {
    }
}

