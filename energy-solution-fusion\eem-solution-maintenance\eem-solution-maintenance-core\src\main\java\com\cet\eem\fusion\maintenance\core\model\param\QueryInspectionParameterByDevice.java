﻿package com.cet.eem.fusion.maintenance.core.model.param;/**
 * <AUTHOR>
 * @className QueryInspectionParameterByDevice
 * @description
 * @date 2021/8/23 14:28
 */

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * @ClassName : QueryInspectionParameterByDevice
 * @Description : 根据设备信息查询巡检参数信息
 * <AUTHOR> jiangzixaun
 * @Date: 2021-08-23 14:28
 */
@Getter
@Setter
@ApiModel(value = "QueryInspectionParameterByDevice", description = "通过设备查询巡检参数")
public class QueryInspectionParameterByDevice {
    /**
     * 设备id
     */
    @NotNull(message = "id不能为空")
    private Long objectId;

    /**
     * 设备类型
     */
    @NotEmpty(message = "设备类型不能为空")
    private String objectLabel;

    /**
     * 参数类型
     * 1 状态量
     * 2 模拟量
     */
    private Integer parameterType;

    private Long schemeId;

    private LocalDateTime startTime;

    private LocalDateTime endTime;
    /**
     * 巡检参数名称
     */
    private Long parameterId;

    private Long  tenantId;
}
