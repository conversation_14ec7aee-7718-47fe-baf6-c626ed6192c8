# 运维插件融合适配任务清单

## 任务概述
对已迁移的`eem-solution-maintenance`插件进行融合适配，确保所有代码符合新的融合框架标准。

## 适配任务清单

### 1. Controller层变更 - [ ]
- [ ] 1.1 返回结果封装类变更
  - 将 `import com.cet.eem.common.model.Result;` 改为 `import com.cet.electric.commons.ApiResult;`
  - 将 `import com.cet.eem.common.model.ResultWithTotal;` 改为 `import com.cet.electric.commons.ApiResult;`
  - 更新返回类型从 `Result<T>` 和 `ResultWithTotal<T>` 到 `ApiResult<T>`
  - 保持使用 `Result.ok()` 方法（来自 `com.cet.eem.fusion.common.entity.Result`）

- [ ] 1.2 权限相关变更
  - 将 `import com.cet.eem.auth.aspect.EnumAndOr;` 改为 `import com.cet.electric.matterhorn.cloud.authservice.sdk.common.enums.EnumAndOr;`

### 2. Service层变更 - [ ]
- [ ] 2.1 产品类型获取方式变更
  - 将 `productDao.queryProducts(GlobalInfoUtils.getProjectId())` 改为 `productDao.queryProducts(GlobalInfoUtils.getTenantId(), Product.class)`

### 3. 实体类变更 - [ ]
- [ ] 3.1 模型类型定义变更（仅适用于Po类）
  - 将 `import com.cet.eem.annotation.ModelLabel;` 改为 `import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;`
  - 将 `import com.cet.eem.model.model.BaseEntity;` 改为 `import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;`
  - 将 `import com.cet.piem.common.constant.TableNameDef;` 改为 `import com.cet.eem.solution.common.def.common.label.ModelLabelDef;`
  - 更新继承关系从 `BaseEntity` 到 `EntityWithName`

### 4. 常量变更 - [ ]
- [ ] 4.1 状态码变更
  - 将 `Result.SUCCESS_CODE` 改为 `ErrorCode.SUCCESS_CODE`

- [ ] 4.2 工具类常量变更
  - 将 `EemCommonUtils.BLANK_STR` 改为 `StringFormatUtils.BLANK_STR`
  - 将 `CommonUtils.APPLICATION_MSEXCEL` 改为 `ContentTypeDef.APPLICATION_MSEXCEL`
  - 将 `CommonUtils.APPLICATION_MS_EXCEL_07` 改为 `ContentTypeDef.APPLICATION_MS_EXCEL_07`
  - 将 `CommonUtils.DOUBLE_CONVERSION_COEFFICIENT` 改为 `NumberCalcUtils.DOUBLE_CONVERSION_COEFFICIENT`

### 5. 方法变更 - [ ]
- [ ] 5.1 项目ID获取方式变更
  - 将所有 `GlobalInfoUtils.getProjectId()` 改为 `GlobalInfoUtils.getTenantId()`

- [ ] 5.2 计算方法变更
  - 将 `CommonUtils.calcDouble()` 改为 `NumberCalcUtils.calcDouble()`

### 6. 类路径变更 - [ ]
- [ ] 6.1 基础类变更
  - 将 `import com.cet.eem.common.model.BaseVo;` 改为 `import com.cet.eem.fusion.common.model.BaseVo;`
  - 将 `import com.cet.eem.fusion.common.util.GlobalInfoUtils;` 改为 `import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;`

- [ ] 6.2 模型相关变更
  - 将 `import com.cet.piem.common.constant.TableNameDef;` 改为 `import com.cet.eem.solution.common.def.common.label.ModelLabelDef;`
  - 将 `import com.cet.piem.common.constant.TableColumnNameDef;` 改为 `import com.cet.eem.solution.common.def.common.label.TableColumnNameDef;`
  - 将 `import com.cet.eem.model.tool.QueryConditionBuilder;` 改为 `import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;`

- [ ] 6.3 工具类变更
  - 将 `import com.cet.piem.common.utils.ProgressUpdaterUtils;` 改为 `import com.cet.eem.solution.common.utils.ProgressUpdaterUtils;`
  - 将 `import com.cet.eem.fusion.common.utils.TimeUtil;` 改为 `import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;`
  - 将 `import com.cet.eem.common.CommonUtils;` 改为 `import com.cet.eem.fusion.common.utils.CommonUtils;`

- [ ] 6.4 DAO相关变更
  - 将 `import com.cet.eem.dao.BaseModelDao;` 改为 `import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;`
  - 将 `import com.cet.eem.bll.common.dao.project.ProductDao;` 改为 `import com.cet.eem.fusion.config.sdk.dao.ProductDao;`

### 7. 验证和测试 - [ ]
- [ ] 7.1 编译验证
  - 确保所有Java文件能够正常编译
  - 检查所有import语句是否正确

- [ ] 7.2 功能验证
  - 验证Controller接口返回格式正确
  - 验证Service层调用正常
  - 验证DAO层数据访问正常

## 执行时间记录
- 任务开始时间: 2025-07-31 16:45
- 当前状态: 准备开始适配

## 注意事项
1. 只进行文档中描述的修改，不做额外变更
2. 保持原有业务逻辑不变
3. 确保所有文件的包名和import语句正确更新
4. 适配完成后进行全面的编译和功能验证
