﻿package com.cet.eem.fusion.maintenance.core.model.devicemanage;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-18
 */
@Data
@AllArgsConstructor
@ApiModel(description = "定时记录参数")
@NoArgsConstructor
public class RunningParamValue {
    private String paramname;

    private Long deviceId;

    private Long dataId;

    private Integer logicalId;


    public RunningParamValue(String paramname, Long deviceId, Long dataId) {
        this.paramname = paramname;
        this.deviceId = deviceId;
        this.dataId = dataId;
        this.logicalId = 1;
    }
}

