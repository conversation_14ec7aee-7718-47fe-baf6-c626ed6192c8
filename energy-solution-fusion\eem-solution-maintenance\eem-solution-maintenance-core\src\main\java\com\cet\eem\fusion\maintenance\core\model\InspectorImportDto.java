﻿package com.cet.eem.fusion.maintenance.core.model;

import com.beust.jcommander.Strings;
import com.cet.eem.fusion.maintenance.core.constant.InspectionTeamType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/11 8:51
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InspectorImportDto {
    /**
     * 对应表格“序号”
     */
    private String index;
    /**
     * 对应表格“班组名称”
     */
    private String inspectTeamName;
    /**
     * 对应表格“班组类型
     */
    private String inspectTeamType;
    /**
     * 对应表格“班组负责人”
     */
    private String dutyOfficer;
    /**
     * 对应表格“班组成员”
     */
    private String dutystaff;

}

