﻿package com.cet.eem.fusion.maintenance.core.service.bff.inspect;

import com.cet.eem.common.model.auth.user.UserGroupVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/7
 */
public interface InspectorBffService {
    /**
     * 查询巡检班组
     *
     * @param tenantId 租户id
     * @return 巡检班组
     */
    List<UserGroupVo> queryInspectorTeam(Long tenantId);

    /**
     * 查询巡检班组，但是不包括用户信息
     *
     * @param tenantId
     * @param name
     * @return
     */
    List<UserGroupVo> queryInspectorTeamWithoutUser(Long tenantId, String name);

    /**
     * 查询巡检班组，不包括权限信息
     *
     * @param tenantId
     * @param name
     * @return
     */
    List<UserGroupVo> queryInspectorTeamWithOutAuth(Long tenantId, String name);
}

