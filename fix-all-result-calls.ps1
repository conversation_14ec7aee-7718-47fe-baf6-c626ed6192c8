# Fix all remaining Result.* calls to ApiResult.*
Write-Host "Fixing all remaining Result.* calls to ApiResult.*..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

function Fix-AllResultCalls {
    param(
        [string]$filePath
    )
    
    if (!(Test-Path $filePath)) {
        return $false
    }
    
    $content = Get-Content $filePath -Raw -Encoding UTF8
    $originalContent = $content
    $changed = $false
    
    # Replace all Result.* calls with ApiResult.*
    $patterns = @(
        @{ Pattern = '\bResult\.ok\('; Replacement = 'ApiResult.ok(' }
        @{ Pattern = '\bResult\.error\('; Replacement = 'ApiResult.error(' }
        @{ Pattern = '\bResult\.fail\('; Replacement = 'ApiResult.fail(' }
        @{ Pattern = '\bResult\.success\('; Replacement = 'ApiResult.success(' }
        @{ Pattern = '\bResultWithTotal\.ok\('; Replacement = 'ApiResult.ok(' }
        @{ Pattern = '\bResultWithTotal\.error\('; Replacement = 'ApiResult.error(' }
        @{ Pattern = '\bResultWithTotal\.fail\('; Replacement = 'ApiResult.fail(' }
        @{ Pattern = '\bResultWithTotal\.success\('; Replacement = 'ApiResult.success(' }
    )
    
    foreach ($patternInfo in $patterns) {
        if ($content -match $patternInfo.Pattern) {
            $content = $content -replace $patternInfo.Pattern, $patternInfo.Replacement
            $changed = $true
            Write-Host "  - Fixed: $($patternInfo.Pattern) -> $($patternInfo.Replacement)" -ForegroundColor Yellow
        }
    }
    
    if ($changed) {
        Set-Content $filePath -Value $content -Encoding UTF8
        Write-Host "✓ Fixed Result calls in: $filePath" -ForegroundColor Green
        return $true
    }
    
    return $false
}

# Process all Java files
Write-Host "Processing Java files to fix all Result.* calls..." -ForegroundColor Cyan
$javaFiles = Get-ChildItem -Path $coreSourcePath -Filter "*.java" -Recurse
$filesUpdated = 0

foreach ($file in $javaFiles) {
    if (Fix-AllResultCalls -filePath $file.FullName) {
        $filesUpdated++
    }
}

Write-Host "`nAll Result.* call fixes completed!" -ForegroundColor Green
Write-Host "Files updated: $filesUpdated" -ForegroundColor Cyan
