baseConfig:
  deviceRelation:
    #启用一对一关联，在设备关联时会进行1对1关联的校验，一个节点只能关联一个采集设备;计费如果需要多回路、分时建线，建议配置为false
    oneToOne: true
    # 设备关联回路号写入规则
    #0:默认只写空
    #1:默认只写1
    #2:默认只写最大值
    #3:默认只写最小值
    #4:全部写入
    logicalIndexWriteScheme: 4
    #根据data id写入回路号;计费的多回路、分时建线，建议配置为：4000004
    logicalIndexWriteByDataId: null
  #启用自己供能，在关联采集设备时若测点id有10000001与4000004，则生成一条自己给自己供能的配置
  energySupply:
    selfEnergy: true
  node:
    #若启用则创建租户后自动创建一个同名的顶级节点，节点乐西由modelLabel配置
    autoCreateRootNode:
      enable: true
      modelLabel: project
    #限制是否只能有一个根节点
    singleRoot: false
    #节点管理查询是过过滤掉设备节点
    query:
      deviceFilter: false
  #导出批大小
  export:
    maxSize: 200
  default:
    #默认能源类型
    energyType: 2
    #能源类型单位,key为能源类型枚举，value为单位枚举
    energyTypeUnit: "{2:18,3:24,7:24,14:19}"
  enum:
    # 是否从表读取子表类型
    subMeterTypeFromTable: false
