﻿package com.cet.eem.fusion.maintenance.core.model.devicemanage.component;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(value = "EditSpareParts", description = "編輯备件")
public class EditSpareParts {
    @Length(max = 50,message = "备件名称长度不能超过50")
    @NotNull(message = "备件名称不能为空")
    private String name;
    @NotNull(message ="备件单位不为空")
    private String unit;
    @NotNull(message ="备件规格型号不能为空")
    private String model;
    @NotNull(message ="备件id不能为空")
    private Long id;
    @NotNull(message ="设备id不能为空")
    private Long sparePartsDeviceId;

}

