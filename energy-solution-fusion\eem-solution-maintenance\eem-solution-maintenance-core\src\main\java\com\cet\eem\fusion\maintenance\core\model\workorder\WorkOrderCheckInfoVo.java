﻿package com.cet.eem.fusion.maintenance.core.model.workorder;


import com.cet.eem.bll.common.model.domain.subject.powermaintenance.Attachment;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/5/27
 */
@Getter
@Setter
public class WorkOrderCheckInfoVo extends EntityWithName {
    @ApiModelProperty("备注信息")
    private String remark;

    private String attachments;

    public List<Attachment> getAttachmentList() {
        return JsonTransferUtils.transferJsonString(attachments, Attachment.class);
    }

    @ApiModelProperty("附件")
    private List<Attachment> attachmentList;

    @JsonProperty(ColumnDef.TASK_ID)
    private String taskId;

    @JsonProperty(ColumnDef.PM_WORKSHEET_ID)
    private Long pmWorkSheetId;

    private String code;

    @JsonProperty(ColumnDef.USERID)
    private Long userId;

    private String userName;

    @JsonProperty(ColumnDef.CREATE_TIME)
    private LocalDateTime createTime;

    @JsonProperty(ColumnDef.FORM_DATA)
    private String formData;
}


