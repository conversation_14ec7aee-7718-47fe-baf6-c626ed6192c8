﻿package com.cet.eem.fusion.maintenance.core.model.handover;

import com.cet.eem.fusion.common.model.Page;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/3/23
 */
@Getter
@Setter
@ApiModel(description = "交接班记录查询")
public class ShiftingDutySearchDto {
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Long teamId;

    private Long projectId;

    private Long tenantId;

    private Page page;
}


