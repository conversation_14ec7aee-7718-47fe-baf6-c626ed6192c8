# Fix Result.ok() returns to ApiResult.ok()
Write-Host "Fixing Result.ok() returns to ApiResult.ok()..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

function Fix-ResultReturns {
    param(
        [string]$filePath
    )
    
    if (!(Test-Path $filePath)) {
        return
    }
    
    $content = Get-Content $filePath -Raw -Encoding UTF8
    $originalContent = $content
    $changed = $false
    
    # Replace Result.ok() with ApiResult.ok()
    if ($content -match "Result\.ok\(") {
        $content = $content -replace "Result\.ok\(", "ApiResult.ok("
        $changed = $true
        Write-Host "  - Fixed Result.ok() returns" -ForegroundColor Yellow
    }
    
    # Replace Result.error() with ApiResult.error()
    if ($content -match "Result\.error\(") {
        $content = $content -replace "Result\.error\(", "ApiResult.error("
        $changed = $true
        Write-Host "  - Fixed Result.error() returns" -ForegroundColor Yellow
    }
    
    # Replace Result.fail() with ApiResult.fail()
    if ($content -match "Result\.fail\(") {
        $content = $content -replace "Result\.fail\(", "ApiResult.fail("
        $changed = $true
        Write-Host "  - Fixed Result.fail() returns" -ForegroundColor Yellow
    }
    
    # Replace ResultWithTotal.ok() with ApiResult.ok()
    if ($content -match "ResultWithTotal\.ok\(") {
        $content = $content -replace "ResultWithTotal\.ok\(", "ApiResult.ok("
        $changed = $true
        Write-Host "  - Fixed ResultWithTotal.ok() returns" -ForegroundColor Yellow
    }
    
    # Replace ResultWithTotal.error() with ApiResult.error()
    if ($content -match "ResultWithTotal\.error\(") {
        $content = $content -replace "ResultWithTotal\.error\(", "ApiResult.error("
        $changed = $true
        Write-Host "  - Fixed ResultWithTotal.error() returns" -ForegroundColor Yellow
    }
    
    if ($changed) {
        Set-Content $filePath -Value $content -Encoding UTF8
        Write-Host "✓ Fixed return statements in: $filePath" -ForegroundColor Green
    }
}

# Process all Java files
Write-Host "Processing Java files to fix return statements..." -ForegroundColor Cyan
$javaFiles = Get-ChildItem -Path $coreSourcePath -Filter "*.java" -Recurse
$filesUpdated = 0

foreach ($file in $javaFiles) {
    $originalSize = (Get-Content $file.FullName -Raw -Encoding UTF8).Length
    Fix-ResultReturns -filePath $file.FullName
    $newSize = (Get-Content $file.FullName -Raw -Encoding UTF8).Length
    
    if ($newSize -ne $originalSize) {
        $filesUpdated++
    }
}

Write-Host "`nReturn statement fixes completed!" -ForegroundColor Green
Write-Host "Files updated: $filesUpdated" -ForegroundColor Cyan
