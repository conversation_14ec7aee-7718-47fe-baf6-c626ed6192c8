﻿package com.cet.eem.fusion.maintenance.core.schedule.strategy;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;
import org.quartz.ScheduleBuilder;
import org.quartz.SimpleScheduleBuilder;
import org.quartz.SimpleTrigger;
import org.springframework.stereotype.Component;

/**
 * @ClassName : OneDayTriggerStrategy
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-26 10:37
 */
@Component(PlanSheetTriggerStrategyKey.ONE_DAY)
public class OneDayTriggerStrategy implements PlanSheetTriggerStrategy<SimpleTrigger> {

    private static final int ONE_DAY_HOURS = 24;

    @Override
    public ScheduleBuilder<SimpleTrigger> buildSchedule(PlanSheet planSheet) {
        SimpleScheduleBuilder simpleScheduleBuilder = SimpleScheduleBuilder.simpleSchedule().withMisfireHandlingInstructionNextWithExistingCount();
        simpleScheduleBuilder.withIntervalInHours(ONE_DAY_HOURS);
        simpleScheduleBuilder.repeatForever();
        return simpleScheduleBuilder;
    }
}

