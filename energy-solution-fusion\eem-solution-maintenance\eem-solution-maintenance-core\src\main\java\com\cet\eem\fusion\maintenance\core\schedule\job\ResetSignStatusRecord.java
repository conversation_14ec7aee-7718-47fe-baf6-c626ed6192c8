﻿package com.cet.eem.fusion.maintenance.core.schedule.job;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInStatusDef;
import com.cet.eem.bll.common.task.TaskSchedule;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectionWorkOrderService;
import com.cet.eem.fusion.maintenance.core.service.singinpoint.SignInStatusRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/5/16
 */
@Component
@Slf4j
public class ResetSignStatusRecord implements TaskSchedule {
    @Autowired
    SignInStatusRecordService signInStatusRecordService;

    /**
     * 判断时间分析是否在运行
     */
    private static boolean isPecCoreRunning = false;


    @Scheduled(cron = "${cet.eem.work-order.signpoint.sign-status-reset-interval}")
    @Override
    public void execute() {
        if (isPecCoreRunning) {
            return;
        }

        log.info("开始执行重置签到点状态...");
        isPecCoreRunning = true;
        try {
            signInStatusRecordService.resetSignInStatus(SignInStatusDef.UNSIGNED);
        } catch (Exception ex) {
            log.error("重置节点运行状态失败:", ex);
        }
        log.info("执行重置签到点状态完成1");
        isPecCoreRunning = false;
    }
}

