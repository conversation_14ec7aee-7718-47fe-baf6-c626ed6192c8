﻿package com.cet.eem.fusion.maintenance.core.controller.bff;

import com.cet.eem.auth.aspect.OperationPermission;
import com.cet.eem.bll.common.def.OperationAuthDef;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.model.workorder.WOCountByTaskTypeVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderSearchVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.fusion.maintenance.core.service.WorkOrderService;
import com.cet.eem.fusion.maintenance.core.service.bff.WorkOrderBffService;
import com.cet.electric.commons.ApiResult;

import com.cet.electric.commons.ApiResult;
import com.cet.eem.common.utils.TimeUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/5/31
 */
public class WorkOrderBffController {
    @Autowired
    WorkOrderService workOrderService;

    @Autowired
    WorkOrderBffService workOrderBffService;

    @ApiOperation(value = "查询工单")
    @PostMapping("/workOrder")
    @OperationPermission(authNames = {OperationAuthDef.WORK_ORDER_BROWSER})
    public ApiResult<List<InspectionWorkOrderDto>> queryWorkOrderList(
            @RequestBody WorkOrderSearchVo dto) {
        if (Objects.isNull(dto.getProjectId())) {
            dto.setProjectId(GlobalInfoUtils.getTenantId());
        }
        return workOrderBffService.queryWorkOrderList(dto);
    }

    @ApiOperation(value = "查询工单数量")
    @PostMapping("/workOrder/count")
    @OperationPermission(authNames = {OperationAuthDef.WORK_ORDER_BROWSER})
    public ApiResult<List<WOCountByTaskTypeVo>> queryWorkOrderCount(
            @RequestBody WorkOrderSearchVo dto) {
        List<WOCountByTaskTypeVo> result = workOrderService.queryWorkOrderCount(dto);
        return ApiResult.ok(result);
    }

    @ApiOperation(value = "查询仅允许操作的工单数量")
    @PostMapping("/onlyOperationWorkOrder/count")
    @OperationPermission(authNames = {OperationAuthDef.WORK_ORDER_BROWSER})
    public ApiResult<List<WOCountByTaskTypeVo>> queryRuntimeWorkOrderCount(
            @RequestBody WorkOrderSearchVo dto) {
        if (Objects.isNull(dto.getEndTime())) {
            dto.setEndTime(TimeUtil.getFirstTimeOfNextDay(LocalDate.now()));
        }
        List<WOCountByTaskTypeVo> result = workOrderService.queryRuntimeWorkOrderCount(dto);
        return ApiResult.ok(result);
    }

    @ApiOperation(value = "校验权限")
    @PostMapping("/checkAuth")
    public ApiResult<Boolean> exportWorkOrder(@RequestParam String code) {
        boolean result = workOrderService.checkAuth(code);
        return ApiResult.ok(result);
    }
}


