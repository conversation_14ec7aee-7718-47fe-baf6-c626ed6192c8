﻿package com.cet.eem.fusion.maintenance.core.model.param;

import com.cet.eem.fusion.common.model.Page;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : QueryInspectionParameterRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-14 09:35
 */
@Getter
@Setter
@ApiModel(value = "QueryInspectionParameterRequest", description = "查询巡检参数")
public class QueryInspectionParameterRequest {
    /**
     * 名称
     */
    private String name;

    /**
     * 参数类型
     * 1 状态量
     * 2 模拟量
     */
    private Integer type;

    /**
     * 分页
     */
    private Page page;
}


