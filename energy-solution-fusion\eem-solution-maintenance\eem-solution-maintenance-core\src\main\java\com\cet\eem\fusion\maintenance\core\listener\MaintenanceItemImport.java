﻿package com.cet.eem.fusion.maintenance.core.listener;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @ClassName : MaintenanceItemImport
 * @Description : 导入维保项目
 * <AUTHOR>
 * @Date: 2021-08-03 11:28
 */
@Data
public class MaintenanceItemImport {

    @ExcelProperty(value = "维保分组", index = 0)
    private String groupName;
    @ExcelProperty(value = "维保方式", index = 1)
    private String maintenanceTypeName;
    @ExcelProperty(value = "维保内容", index = 2)
    private String count;
    @ExcelProperty(value = "零部件类型", index = 3)
    private String sparePartName;
    @ExcelProperty(value = "零部件数量", index = 4)
    private Double number;
    @ExcelProperty(value = "零部件单位", index = 5)
    private String unit;


}
