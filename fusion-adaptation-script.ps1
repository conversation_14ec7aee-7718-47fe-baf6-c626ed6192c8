# Fusion Adaptation Script for Maintenance Plugin
Write-Host "Starting fusion adaptation for eem-solution-maintenance plugin..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"
$adaptationLog = @()

# Define replacement mappings based on fusion adaptation document
$importReplacements = @{
    # Controller layer changes
    "import com.cet.eem.common.model.Result;" = "import com.cet.electric.commons.ApiResult;`nimport com.cet.eem.fusion.common.entity.Result;"
    "import com.cet.eem.common.model.ResultWithTotal;" = "import com.cet.electric.commons.ApiResult;"
    "import com.cet.eem.auth.aspect.EnumAndOr;" = "import com.cet.electric.matterhorn.cloud.authservice.sdk.common.enums.EnumAndOr;"
    
    # Basic class changes
    "import com.cet.eem.common.model.BaseVo;" = "import com.cet.eem.fusion.common.model.BaseVo;"
    "import com.cet.eem.fusion.common.util.GlobalInfoUtils;" = "import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;"
    "import com.cet.eem.bll.common.util.GlobalInfoUtils;" = "import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;"
    
    # Model related changes
    "import com.cet.piem.common.constant.TableNameDef;" = "import com.cet.eem.solution.common.def.common.label.ModelLabelDef;"
    "import com.cet.piem.common.constant.TableColumnNameDef;" = "import com.cet.eem.solution.common.def.common.label.TableColumnNameDef;"
    "import com.cet.eem.annotation.ModelLabel;" = "import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;"
    "import com.cet.eem.model.model.BaseEntity;" = "import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;"
    
    # Tool class changes
    "import com.cet.piem.common.utils.ProgressUpdaterUtils;" = "import com.cet.eem.solution.common.utils.ProgressUpdaterUtils;"
    "import com.cet.eem.fusion.common.utils.TimeUtil;" = "import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;"
    "import com.cet.eem.common.CommonUtils;" = "import com.cet.eem.fusion.common.utils.CommonUtils;"
    "import com.cet.eem.model.tool.QueryConditionBuilder;" = "import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;"
    "import com.cet.eem.model.tool.ParentQueryConditionBuilder;" = "import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;"
    "import com.cet.eem.model.tool.ModelServiceUtils;" = "import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;"
    
    # DAO related changes
    "import com.cet.eem.dao.BaseModelDao;" = "import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;"
    "import com.cet.eem.bll.common.dao.project.ProductDao;" = "import com.cet.eem.fusion.config.sdk.dao.ProductDao;"
    "import com.cet.eem.dao.ModelDaoImpl;" = "import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;"
    
    # Exception handling
    "import com.cet.eem.common.exception.BusinessBaseException;" = "import com.cet.eem.fusion.common.exception.BusinessBaseException;"
    
    # Page related
    "import com.cet.eem.common.model.Page;" = "import com.cet.eem.fusion.common.model.Page;"
    "import com.cet.eem.common.page.PageUtils;" = "import com.cet.eem.fusion.common.utils.page.PageUtils;"
    
    # File utils
    "import com.cet.eem.common.file.FileUtils;" = "import com.cet.eem.fusion.common.utils.file.FileUtils;"
    
    # User related
    "import com.cet.eem.common.model.auth.user.UserVo;" = "import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;"
    
    # Other common changes
    "import com.cet.eem.common.definition.ColumnDef;" = "import com.cet.eem.fusion.common.def.common.ColumnDef;"
    "import com.cet.eem.common.constant.EnumOperationType;" = "import com.cet.eem.fusion.common.def.common.EnumOperationType;"
    "import com.cet.eem.common.parse.JsonTransferUtils;" = "import com.cet.eem.fusion.common.utils.JsonTransferUtils;"
    "import com.cet.eem.common.definition.SplitCharDef;" = "import com.cet.eem.fusion.common.def.common.SplitCharDef;"
    "import com.cet.eem.common.ParamUtils;" = "import com.cet.eem.fusion.common.utils.ParamUtils;"
    "import com.cet.eem.common.definition.NodeLabelDef;" = "import com.cet.eem.fusion.common.def.label.NodeLabelDef;"
    "import com.cet.eem.common.constant.QueryType;" = "import com.cet.eem.fusion.common.def.base.QueryType;"
    "import com.cet.eem.common.constant.EnergyTypeDef;" = "import com.cet.eem.fusion.common.def.base.EnergyTypeDef;"
    
    # Model base changes
    "import com.cet.eem.model.base.QueryCondition;" = "import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;"
    "import com.cet.eem.model.base.ModelSingeWriteVo;" = "import com.cet.eem.fusion.common.modelutils.model.base.ModelSingeWriteVo;"
    
    # Conditions changes
    "import com.cet.eem.conditions.query.LambdaQueryWrapper;" = "import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;"
    "import com.cet.eem.conditions.update.LambdaUpdateWrapper;" = "import com.cet.eem.fusion.common.modelutils.conditions.update.LambdaUpdateWrapper;"
}

# Method and constant replacements
$codeReplacements = @{
    # Method changes
    "GlobalInfoUtils.getProjectId()" = "GlobalInfoUtils.getTenantId()"
    "CommonUtils.calcDouble(" = "NumberCalcUtils.calcDouble("
    
    # Return type changes
    "Result<" = "ApiResult<"
    "ResultWithTotal<" = "ApiResult<"
    
    # Constant changes
    "Result.SUCCESS_CODE" = "ErrorCode.SUCCESS_CODE"
    "EemCommonUtils.BLANK_STR" = "StringFormatUtils.BLANK_STR"
    "CommonUtils.APPLICATION_MSEXCEL" = "ContentTypeDef.APPLICATION_MSEXCEL"
    "CommonUtils.APPLICATION_MS_EXCEL_07" = "ContentTypeDef.APPLICATION_MS_EXCEL_07"
    "CommonUtils.DOUBLE_CONVERSION_COEFFICIENT" = "NumberCalcUtils.DOUBLE_CONVERSION_COEFFICIENT"
    "CommonUtils.formatDoubleWithOutScientificNotation" = "StringFormatUtils.formatDoubleWithOutScientificNotation"
    
    # Base class changes
    "extends BaseEntity" = "extends EntityWithName"
    
    # Table name changes
    "TableNameDef." = "ModelLabelDef."
}

function Update-JavaFile {
    param(
        [string]$filePath
    )
    
    if (!(Test-Path $filePath)) {
        return
    }
    
    $content = Get-Content $filePath -Raw -Encoding UTF8
    $originalContent = $content
    $changed = $false
    
    # Apply import replacements
    foreach ($oldImport in $importReplacements.Keys) {
        $newImport = $importReplacements[$oldImport]
        if ($content -match [regex]::Escape($oldImport)) {
            $content = $content -replace [regex]::Escape($oldImport), $newImport
            $changed = $true
            Write-Host "  - Updated import: $oldImport -> $newImport" -ForegroundColor Yellow
        }
    }
    
    # Apply code replacements
    foreach ($oldCode in $codeReplacements.Keys) {
        $newCode = $codeReplacements[$oldCode]
        if ($content -match [regex]::Escape($oldCode)) {
            $content = $content -replace [regex]::Escape($oldCode), $newCode
            $changed = $true
            Write-Host "  - Updated code: $oldCode -> $newCode" -ForegroundColor Yellow
        }
    }
    
    if ($changed) {
        Set-Content $filePath -Value $content -Encoding UTF8
        $adaptationLog += "Updated: $filePath"
        Write-Host "✓ Updated file: $filePath" -ForegroundColor Green
    }
}

# Process all Java files in core module
Write-Host "Processing Java files in core module..." -ForegroundColor Cyan
$javaFiles = Get-ChildItem -Path $coreSourcePath -Filter "*.java" -Recurse

foreach ($file in $javaFiles) {
    Write-Host "Processing: $($file.FullName)" -ForegroundColor Gray
    Update-JavaFile -filePath $file.FullName
}

# Summary
Write-Host "`n=== FUSION ADAPTATION SUMMARY ===" -ForegroundColor Cyan
Write-Host "Total files processed: $($javaFiles.Count)" -ForegroundColor White
Write-Host "Files updated: $($adaptationLog.Count)" -ForegroundColor Green

if ($adaptationLog.Count -gt 0) {
    Write-Host "`nUpdated files:" -ForegroundColor Yellow
    foreach ($log in $adaptationLog) {
        Write-Host "  $log" -ForegroundColor Gray
    }
}

Write-Host "`nFusion adaptation completed!" -ForegroundColor Green
