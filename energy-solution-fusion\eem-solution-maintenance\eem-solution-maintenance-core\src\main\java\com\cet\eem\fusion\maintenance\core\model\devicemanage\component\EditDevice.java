﻿package com.cet.eem.fusion.maintenance.core.model.devicemanage.component;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @ClassName : EditDevice
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2021-05-14 16:10
 */
@Getter
@Setter
@ApiModel(value = "EditDevice", description = "编辑设备")
public class EditDevice {
    @NotNull(message = "设备规格不能为空")
    private String model;
    @NotNull(message = "设备名称不能为空")
    private String name;
    @NotNull(message = "设备类型id不能为空")
    private Long objectLabelId;
    @NotNull(message = "设备id不能为空")
    private Long id;
    @NotNull(message = "系统id不能为空")
    private Long deviceSystemId;

    /**
     * 所属公司
     */
    private String company;

    /**
     * 关键技术参数
     */
    private String criticalParams;

    /**
     * 二级库备品备件id
     */
    private Long secondarySparePartsId;
}
