﻿package com.cet.eem.fusion.maintenance.core.service.repair;

import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderCountDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderFormBatchSubmitParam;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderFormSubmitParam;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.*;
import com.cet.eem.fusion.maintenance.core.model.workorder.repair.*;
import com.cet.eem.fusion.maintenance.core.schedule.event.CreateOrderCommand;
import com.cet.eem.fusion.maintenance.core.service.WorkOrderServiceCallBackParam;
import com.cet.eem.fusion.maintenance.core.service.WorkOrderServiceCallBackResult;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.workflow.common.model.ProcessInstanceResponse;
import com.cet.electric.workflow.common.model.node.config.UserTaskConfig;

import java.util.List;

/**
 * 维修工单
 *
 * <AUTHOR>
 * @date 4/12/2021
 */
public interface RepairWorkOrderService {
    /**
     * 查询工单列表
     *
     * @param dto
     * @return
     */
    <T extends InspectionWorkOrderDto> ApiResult<List<T>> queryWorkOrderList(RepairSearchVo dto, Class<T> clazz);

    /**
     * 查询工单
     *
     * @param dto
     * @param clazz
     * @param <T>
     * @return
     */
    <T extends InspectionWorkOrderDto> ApiResult<List<T>> queryWorkOrderByNode(RepairByNodeSearchVo dto, Class<T> clazz);

    /**
     * 统计工单数量
     *
     * @param dto
     * @return
     */
    List<WorkOrderCountDto> queryWorkOrderCount(InspectionCountSearchDto dto);

    /**
     * 新增或者更新工单
     *
     * @param dto
     * @return
     */
    ProcessInstanceResponse createWorkOrder(RepairWorkOrderAddDto dto);

    /**
     * 根据工单id查询工单
     */
    InspectionWorkOrderDto queryWorkOrder(Long workOrderId);

    /**
     * 根据工单编号查询工单
     *
     * @param code
     * @return
     */
    InspectionWorkOrderDto queryWorkOrder(String code);

    /**
     * 查询工单节点配置信息
     *
     * @param code
     * @return
     */
    UserTaskConfig queryTaskConfig(String code);

    /**
     * 更新工单
     *
     * @param inspectParamsUpdateVo
     */
    void updateWorkOrder(RepairParamsWriteVo inspectParamsUpdateVo);

    /**
     * 提交审批参数
     *
     * @param repairParamsWriteVo
     */
    void submitInspectParams(RepairParamsWriteVo repairParamsWriteVo);

    /**
     * 根据巡检工单创建维修工单
     *
     * @param param
     */
    WorkOrderServiceCallBackResult createWorkOrderByInspectWO(WorkOrderServiceCallBackParam param);

    /**
     * 保存维保内容
     *
     * @param param
     */
    WorkOrderServiceCallBackResult saveMaintenanceContent(WorkOrderServiceCallBackParam param);

    /**
     * 更新超时状态
     */
    void updateOverTimeStatus();

    /**
     * 更新维修工单关联的事件状态
     *
     * @param param
     * @param userId
     */
    void updateSystemStatus(WorkOrderFormSubmitParam param, Long userId);

    /**
     * 获得维修工单配置
     *
     * @return
     */
    RepairConfigVO getRepairConfig() throws Exception;
}


