﻿package com.cet.eem.fusion.maintenance.core.controller.bff.repair.app;

import com.cet.eem.auth.aspect.OperationPermission;
import com.cet.eem.bll.common.def.OperationAuthDef;
import com.cet.eem.fusion.maintenance.core.model.workorder.app.WorkOrderCountDto;
import com.cet.eem.fusion.maintenance.core.service.bff.repair.app.RepairWorkOrderMobileBffService;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/12
 */
public class RepairWorkOrderBffMobileController {
    @Autowired
    private RepairWorkOrderMobileBffService repairWorkOrderMobileBffService;

    @ApiOperation(value = "查询维修工单")
    @PostMapping("")
    @OperationPermission(authNames = {OperationAuthDef.REPAIR_WORK_ORDER_BROWSER})
    public ApiResult<List<WorkOrderCountDto>> queryWorkOrderList(
            @RequestBody(required = false) @ApiParam(name = "page", value = "分页信息") Page page) {
        List<WorkOrderCountDto> result = repairWorkOrderMobileBffService.queryWorkOrder(page);
        return ApiResult.ok(result);
    }
}


