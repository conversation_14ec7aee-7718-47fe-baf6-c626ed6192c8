﻿package com.cet.eem.fusion.maintenance.core.model.plan;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.DevicePlanRelationship;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;
import com.cet.eem.common.definition.ModelLabelDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * @ClassName : MaintenancePlanSheetVo
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-22 09:58
 */
@Getter
@Setter
public class MaintenancePlanSheetVo extends PlanSheetBaseVo {

    /**
     * 等级名称
     */
    private String worksheetTaskLevelName;


    public MaintenancePlanSheetVo(PlanSheet planSheet) {
        BeanUtils.copyProperties(planSheet, this);
    }
}

