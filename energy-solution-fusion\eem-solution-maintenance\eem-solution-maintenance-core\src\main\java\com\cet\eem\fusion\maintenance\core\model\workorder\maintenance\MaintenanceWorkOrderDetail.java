﻿package com.cet.eem.fusion.maintenance.core.model.workorder.maintenance;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.DevicePlanRelationship;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.ProcessFlowUnit;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.Attachment;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderPo;
import com.cet.eem.fusion.common.model.BaseVo;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Objects;

/**
 * @ClassName : MaintenanceWorkOrderDetail
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-26 14:03
 */
@Getter
@Setter
public class MaintenanceWorkOrderDetail extends WorkOrderPo {

    /**
     * 执行人
     */
    @ApiModelProperty("执行人")
    private List<String> executorNames;

    /**
     * 维保对象
     */
    @ApiModelProperty("关联的设备")
    @JsonProperty(WorkOrderDef.DEVICE_PLAN_RELATIONSHIP_MODEL)
    private List<DevicePlanRelationship> devicePlanRelationshipList;

    /**
     * 维保对象名称
     */
    @ApiModelProperty("维保对象")
    private BaseVo target;

    /**
     * 工单来源
     */
    @ApiModelProperty("工单来源")
    private String sourceTypeName;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人姓名")
    @JsonProperty(WorkOrderDef.CREATOR_NAME)
    private String creatorName;

    /**
     * 责任班组
     */
    @ApiModelProperty("责任班组")
    private String teamName;

    /**
     * 任务等级
     */
    @JsonProperty("tasklevelname")
    private String taskLevelName;

    /**
     * 计划结束时间
     */
    @ApiModelProperty("计划结束时间")
    @JsonProperty("executeendtimeplan")
    private Long executeEndTimePlan;

    /**
     * 计划结束时间
     */
    @ApiModelProperty("计划结束时间")
    @JsonProperty("executeendtime")
    private Long executeEndTime;

    /**
     * 维保项目
     */
    @ApiModelProperty("维保项目")
    private List<MaintenanceItemExtendVo> maintenanceItemExtendList;

    /**
     * 备件信息
     */
    @ApiModelProperty("备件信息")
    private List<SparePartNumber> sparePartNumbers;

    /**
     * 附件信息
     */
    private List<Attachment> attachmentList;

    /**
     * 处理描述
     */
    @ApiModelProperty("处理描述")
    @JsonProperty(WorkOrderDef.HANDLE_DESCRIPTION)
    private String handleDescription;

    /**
     * 流程表
     */
    @ApiModelProperty("流程表")
    private List<ProcessFlowUnit> processFlowUnits;

    public void generateSourceTypeName() {
        if (Objects.isNull(this.getCreator())) {
            this.sourceTypeName = "自动创建";
        } else {
            this.sourceTypeName = "手动创建";
        }
    }
}


