﻿package com.cet.eem.fusion.maintenance.core.model.devicemanage.component;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SparePartsDevice;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : DeviceWithSpareParts
 * @Description : 接收设备和其底下单个备件的类
 * <AUTHOR> jiangzixuan
 * @Date: 2021-05-24 08:31
 */
@Getter
@Setter
public class DeviceWithSpareParts extends SparePartsDevice {
    /**
     * 备件名称
     */
    private String SparePartsName;
    /**
     * 备件型号
     */
    private String SparePartsModel;
}

