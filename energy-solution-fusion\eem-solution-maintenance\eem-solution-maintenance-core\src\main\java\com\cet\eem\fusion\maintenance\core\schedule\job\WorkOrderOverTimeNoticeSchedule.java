﻿package com.cet.eem.fusion.maintenance.core.schedule.job;

import com.cet.eem.bll.common.task.TaskSchedule;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectionWorkOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/5/16
 */
@Component
@Slf4j
public class WorkOrderOverTimeNoticeSchedule implements TaskSchedule {
    @Autowired
    InspectionWorkOrderService workOrderService;

    /**
     * 判断时间分析是否在运行
     */
    private boolean isPecCoreRunning = false;

    @Scheduled(cron = "${cet.eem.work-order.inspect.check-over-time-notice.interval}")
    @Override
    public void execute() {
        if (isPecCoreRunning) {
            return;
        }

        isPecCoreRunning = true;
        try {
            workOrderService.noticeOverTimeStatus();
        } catch (Exception ex) {
            log.error("执行巡检工单超时推送任务发生异常:", ex);
        }

        isPecCoreRunning = false;
    }
}

