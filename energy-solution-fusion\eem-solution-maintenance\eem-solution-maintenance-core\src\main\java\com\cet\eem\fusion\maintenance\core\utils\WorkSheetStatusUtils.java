﻿package com.cet.eem.fusion.maintenance.core.utils;

import com.cet.eem.bll.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.fusion.maintenance.core.def.WorkSheetStatusDef;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/6/20
 */
@Component
public class WorkSheetStatusUtils {
    @Autowired
    ModelServiceUtils modelServiceUtils;

    /**
     * 根据工单类型查询工单状态映射
     *
     * @param taskType
     * @param workSheetStatusMap
     * @return
     */
    public Map<Integer, String> getWorkSheetStatusMapByTaskType(Integer taskType, Map<Integer, Map<Integer, String>> workSheetStatusMap) {
        return getWorkSheetStatusMap(taskType, workSheetStatusMap);
    }

    /**
     * 根据工单类型查询工单状态映射
     *
     * @param taskType
     * @return
     */
    public Map<Integer, String> getWorkSheetStatusMapByTaskType(Integer taskType) {
        Map<Integer, Map<Integer, String>> workSheetStatusMap = getWorkSheetStatusMap();
        return getWorkSheetStatusMap(taskType, workSheetStatusMap);
    }

    private Map<Integer, String> getWorkSheetStatusMap(Integer taskType, Map<Integer, Map<Integer, String>> workSheetStatusMap) {
        Map<Integer, String> result = workSheetStatusMap.get(taskType);
        if (result == null) {
            return modelServiceUtils.getEnumByLabel(ModelLabelDef.WORK_SHEET_STATUS);
        }

        return result;
    }

    public Map<Integer, Map<Integer, String>> getWorkSheetStatusMap() {
        Map<Integer, Map<Integer, String>> result = new HashMap<>();
        result.put(WorkSheetTaskType.INSPECTION, getInspectorWorkSheetStatusMap());
        result.put(WorkSheetTaskType.REPAIR, getRepairWorkSheetStatusMap());
        result.put(WorkSheetTaskType.MAINTENANCE, getMaintenanceWorkSheetStatusMap());

        return result;
    }

    public Map<Integer, String> getInspectorWorkSheetStatusMap() {
        HashMap<Integer, String> map = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        map.put(WorkSheetStatusDef.TO_BE_SENT, "待巡检");
        map.put(WorkSheetStatusDef.ALREADY_SENT, "已派");
        map.put(WorkSheetStatusDef.AUDITED, "待审核");
        map.put(WorkSheetStatusDef.TO_BE_AUDITED, "被退回");
        map.put(WorkSheetStatusDef.WASTED, "废弃");
        map.put(WorkSheetStatusDef.ACCOMPLISHED, "已完成");
        map.put(WorkSheetStatusDef.ABNORMAL, "异常");
        map.put(WorkSheetStatusDef.OVERTIME, "已超时");

        return map;
    }

    public Map<Integer, String> getRepairWorkSheetStatusMap() {
        HashMap<Integer, String> map = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        map.put(WorkSheetStatusDef.TO_BE_SENT, "待确认");
        map.put(WorkSheetStatusDef.ALREADY_SENT, "待维修");
        map.put(WorkSheetStatusDef.AUDITED, "待审核");
        map.put(WorkSheetStatusDef.TO_BE_AUDITED, "被退回");
        map.put(WorkSheetStatusDef.WASTED, "废弃");
        map.put(WorkSheetStatusDef.ACCOMPLISHED, "已完成");
        map.put(WorkSheetStatusDef.ABNORMAL, "异常");
        map.put(WorkSheetStatusDef.OVERTIME, "已超时");

        return map;
    }

    public Map<Integer, String> getMaintenanceWorkSheetStatusMap() {
        HashMap<Integer, String> map = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        map.put(WorkSheetStatusDef.TO_BE_SENT, "待维保");
        map.put(WorkSheetStatusDef.ALREADY_SENT, "已派");
        map.put(WorkSheetStatusDef.AUDITED, "待审核");
        map.put(WorkSheetStatusDef.TO_BE_AUDITED, "被退回");
        map.put(WorkSheetStatusDef.WASTED, "废弃");
        map.put(WorkSheetStatusDef.ACCOMPLISHED, "已完成");
        map.put(WorkSheetStatusDef.ABNORMAL, "异常");
        map.put(WorkSheetStatusDef.OVERTIME, "已超时");

        return map;
    }
}


