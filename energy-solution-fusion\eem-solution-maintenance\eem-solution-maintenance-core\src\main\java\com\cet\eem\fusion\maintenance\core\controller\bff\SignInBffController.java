﻿package com.cet.eem.fusion.maintenance.core.controller.bff;

import cn.hutool.json.JSONObject;
import com.cet.eem.auth.aspect.OperationPermission;
import com.cet.eem.bll.common.def.OperationAuthDef;
import com.cet.eem.bll.common.log.annotation.OperationLog;
import com.cet.eem.bll.common.log.constant.EEMOperationLogType;
import com.cet.eem.bll.common.log.constant.EnumOperationSubType;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInGroup;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInPoint;
import com.cet.eem.bll.common.model.ext.modelentity.EemQueryCondition;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.SignInGroupWithSubLayer;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.SignInPointWithSupLayer;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.dao.SignInGroupDao;
import com.cet.eem.fusion.maintenance.core.model.sign.*;
import com.cet.eem.fusion.maintenance.core.model.workorder.SignInPointStatusCount;
import com.cet.eem.fusion.maintenance.core.service.singinpoint.SignInService;
import com.cet.eem.fusion.maintenance.core.service.singinpoint.SignInStatusRecordService;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.commons.ApiResult;

import com.cet.eem.common.model.peccore.Node;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;

/**
 * @ClassName : SignInBffController
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-03-12 16:38
 */

public class SignInBffController {

    @Autowired
    private SignInService signInService;

    @Autowired
    private SignInGroupDao signInGroupDao;

    @Autowired
    SignInStatusRecordService signInStatusRecordService;

    @ApiOperation(value = "查询当前项目签到组")
    @GetMapping("/group")
    public ApiResult<List<QueryCurrentProjectSignInGroupResult>> queryCurrentProjectSignInGroup() {
        List<SignInGroupWithSubLayer> signInGroupWithSubLayers = signInService.queryCurrentProjectSignInGroup();
        return ApiResult.ok(transferTo(signInGroupWithSubLayers));
    }

    @ApiOperation(value = "新建签到组")
    @OperationLog(operationType = EEMOperationLogType.REGISTRATION_GROUP, subType = EnumOperationSubType.ADD, description = "【新建签到组】")
    @PostMapping("/group")
    public ApiResult<SignInGroup> createSignInGroup(@Valid @RequestBody CreateSignInGroupRequest createSignInGroupRequest) {
        SignInGroup signInGroup = signInService.createSignInGroup(createSignInGroupRequest);
        return ApiResult.ok(signInGroup);
    }

    @ApiOperation(value = "编辑签到组")
    @OperationLog(operationType = EEMOperationLogType.REGISTRATION_GROUP, subType = EnumOperationSubType.UPDATE, description = "【更新签到组】")
    @PatchMapping("/group")
    public ApiResult<SignInGroup> editSignInGroup(@Valid @RequestBody EditSignInGroupRequest editSignInGroupRequest) {
        SignInGroup signInGroup = signInService.editSignInGroup(editSignInGroupRequest);
        return ApiResult.ok(signInGroup);
    }

    @ApiOperation(value = "删除签到组")
    @OperationLog(operationType = EEMOperationLogType.REGISTRATION_GROUP, subType = EnumOperationSubType.DELETE, description = "【删除签到组】")
    @DeleteMapping("/group")
    public ApiResult<Void> deleteSignInGroup(@RequestBody Collection<Long> ids) {
        signInService.deleteSignInGroup(ids);
        return ApiResult.ok();
    }

    @ApiOperation(value = "根据签到点分组查询签到点信息")
    @GetMapping("/group/{id}")
    public ApiResult<SignInGroup> selectById(@PathVariable("id") Long id) {
        SignInGroup signInGroup = signInGroupDao.selectById(id);
        return ApiResult.ok(signInGroup);
    }

    @ApiOperation(value = "查询签到点下的签到设备")
    @GetMapping("/point")
    public ApiResult<List<JSONObject>> querySignInEquipmentInPoint(@RequestParam Long signInPointId) {
        List<JSONObject> jsonObjectList = signInService.querySignInEquipmentInPoint(signInPointId);
        return ApiResult.ok(jsonObjectList);
    }

    @ApiOperation(value = "新建签到点")
    @OperationLog(operationType = EEMOperationLogType.REGISTRATION_POINT, subType = EnumOperationSubType.ADD, description = "【新建签到点】")
    @PostMapping("/point")
    public ApiResult<Map<String, Object>> createSignInPoint(@Valid @RequestBody CreateSignInPointRequest createSignInGroupRequest) {
        return ApiResult.ok(signInService.createSignInPoint(createSignInGroupRequest));
    }

    @ApiOperation(value = "批量新建签到点")
    @OperationLog(operationType = EEMOperationLogType.REGISTRATION_POINT, subType = EnumOperationSubType.ADD, description = "【批量新建签到点】")
    @PostMapping("/points")
    public ApiResult<Map<String, Object>> createBatchSignInPoints(@Valid @RequestBody List<CreateSignInPointRequest> createSignInGroupRequests, HttpServletRequest request) {
        Long projectId = Long.valueOf(request.getHeader("projectId"));
        signInService.createSignInPointBatch(createSignInGroupRequests, projectId);
        return ApiResult.ok();
    }

    @ApiOperation(value = "编辑签到点")
    @OperationLog(operationType = EEMOperationLogType.REGISTRATION_POINT, subType = EnumOperationSubType.UPDATE, description = "【编辑签到点】")
    @PatchMapping("/point")
    public ApiResult<SignInPoint> editSignInPoint(@Valid @RequestBody EditSignInPointRequest editSignInPointRequest) {
        return ApiResult.ok(signInService.editSignInPoint(editSignInPointRequest));
    }

    @ApiOperation(value = "删除签到点")
    @OperationLog(operationType = EEMOperationLogType.REGISTRATION_POINT, subType = EnumOperationSubType.DELETE, description = "【删除签到点】")
    @DeleteMapping("/point/{signInGroupId}")
    public ApiResult<Void> deleteSignInPoint(@PathVariable("signInGroupId") Long signInGroupId, @RequestBody List<Long> ids) {
        signInService.deleteSignInPoint(signInGroupId, ids);
        return ApiResult.ok();
    }

    @ApiOperation(value = "签到点排序")
    @PostMapping("/point/sort/{signInGroupId}")
    public ApiResult<List<SignInPoint>> editSignInPoint(@PathVariable("signInGroupId") Long signInGroupId, @RequestBody List<SortSignInPointRequest> sortSignInPointRequestList) {
        return ApiResult.ok(signInService.sortSignInPoint(signInGroupId, sortSignInPointRequestList));
    }

    @ApiOperation(value = "删除巡检设备")
    @OperationLog(operationType = EEMOperationLogType.REGISTRATION_EQUIPMENT, subType = EnumOperationSubType.DELETE, description = "【删除巡检设备】")
    @DeleteMapping("/equipment/{signInPoint}")
    public ApiResult<List<Map<String, Object>>> deleteSignInEquipment(@PathVariable("signInPoint") Long signInPoint, @RequestBody List<BaseEntity> baseEntities) {
        return ApiResult.ok(signInService.deleteSignInEquipment(signInPoint, baseEntities));
    }

    @ApiOperation(value = "查询巡检设备树")
    @PostMapping("/equipment")
    public ApiResult<List<Map<String, Object>>> querySignInEquipmentInGroup(@RequestParam("signInGroup") Long signInGroup,
                                                                         @RequestBody EemQueryCondition condition) {
        return ApiResult.ok(signInService.querySignInEquipmentInGroup(signInGroup, condition));
    }

    @ApiOperation(value = "根据项目查询签到点信息")
    @GetMapping("/signInPointByProject")
    public ApiResult<List<BaseVo>> querySignInEquipmentInGroup(
            @RequestParam(required = false) @ApiParam(name = "name", value = "班组名") String name) {
        return ApiResult.ok(signInService.querySignInPointByProjectId(name));
    }

    @ApiOperation(value = "签到点分组关联图形")
    @PutMapping("/relateGraph")
    @OperationPermission(authNames = {OperationAuthDef.SignInPoint.SIGNG_ROUP_RELEATE_GRAPH})
    @OperationLog(operationType = EEMOperationLogType.REGISTRATION_GROUP, subType = EnumOperationSubType.UPDATE, description = "【签到点分组关联图形】")
    public ApiResult<List<Map<String, Object>>> querySignInEquipmentInGroup(@RequestParam("signGroupId") Long signGroupId,
                                                                         @RequestBody Node node) {
        signInService.relateGraph(signGroupId, node);
        return ApiResult.ok();
    }

    @ApiOperation(value = "签到点状态统计")
    @PutMapping("/signInPointStatusCount")
    public ApiResult<SignInPointStatusCount> querySignInPointStatusCount(@RequestParam(name = "signGroupId", required = false) Long signGroupId) {
        SignInPointStatusCount signInPointStatusCount = signInStatusRecordService.querySignInPointStatusCount(signGroupId);
        return ApiResult.ok(signInPointStatusCount);
    }

    private List<QueryCurrentProjectSignInGroupResult> transferTo(List<SignInGroupWithSubLayer> signInGroupWithSubLayers) {
        if (CollectionUtils.isEmpty(signInGroupWithSubLayers)) {
            return Collections.emptyList();
        }
        List<QueryCurrentProjectSignInGroupResult> queryCurrentProjectSignInGroupResultList = new ArrayList<>(signInGroupWithSubLayers.size());
        Map<SignInPoint, Set<Long>> map = new HashMap<>();
        for (SignInGroupWithSubLayer signInGroupWithSubLayer : signInGroupWithSubLayers) {
            if (CollectionUtils.isEmpty(signInGroupWithSubLayer.getSignInPointList())) {
                continue;
            }
            for (SignInPoint signInPoint : signInGroupWithSubLayer.getSignInPointList()) {
                Set<Long> signInGroupIds = map.computeIfAbsent(signInPoint, s -> new HashSet<>());
                signInGroupIds.add(signInGroupWithSubLayer.getId());
            }
        }
        for (SignInGroupWithSubLayer signInGroupWithSubLayer : signInGroupWithSubLayers) {
            QueryCurrentProjectSignInGroupResult queryCurrentProjectSignInGroupResult = new QueryCurrentProjectSignInGroupResult();
            queryCurrentProjectSignInGroupResultList.add(queryCurrentProjectSignInGroupResult);
            BeanUtils.copyProperties(signInGroupWithSubLayer, queryCurrentProjectSignInGroupResult);
            if (CollectionUtils.isEmpty(signInGroupWithSubLayer.getSignInPointList())) {
                continue;
            }
            List<SignInPointWithSupLayer> signInPointWithSupLayerList = new ArrayList<>(signInGroupWithSubLayer.getSignInPointList().size());
            queryCurrentProjectSignInGroupResult.setSignInPointList(signInPointWithSupLayerList);
            for (SignInPoint signInPoint : signInGroupWithSubLayer.getSignInPointList()) {
                SignInPointWithSupLayer signInPointWithSupLayer = new SignInPointWithSupLayer();
                signInPointWithSupLayerList.add(signInPointWithSupLayer);
                BeanUtils.copyProperties(signInPoint, signInPointWithSupLayer);
                signInPointWithSupLayer.setSignInGroupIds(map.get(signInPoint));
            }
        }
        return queryCurrentProjectSignInGroupResultList;
    }

    @ApiOperation(value = "根据签到点下载签到点二维码")
    @GetMapping("/downloadQrCode/signPoint")
    public ApiResult<Object> downloadSignPointQrCode(@RequestParam(name = "signPointId") Long signPointId) throws IOException {
        signInService.downloadSignPointQrCode(signPointId, GlobalInfoUtils.getHttpResponse());
        return null;
    }

    @ApiOperation(value = "根据签到点分组下载签到点二维码")
    @GetMapping("/downloadQrCode/signGroup")
    public ApiResult<Object> downloadSignGroupQrCode(@RequestParam(name = "signGroupId") Long signGroupId) throws IOException {
        signInService.downloadSignGroupQrCode(signGroupId, GlobalInfoUtils.getHttpResponse());
        return null;
    }

    @ApiOperation(value = "根据项目下载签到点二维码")
    @GetMapping("/downloadQrCode/project")
    public ApiResult<Object> downProjectLoadQrCode(@RequestParam(name = "projectId") Long projectId) throws IOException {
        signInService.downProjectLoadQrCode(projectId, GlobalInfoUtils.getHttpResponse());
        return null;
    }
}



