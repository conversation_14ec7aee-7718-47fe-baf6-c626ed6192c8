﻿package com.cet.eem.fusion.maintenance.core.model.workorder.inspection.recordsheet;

import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.model.Page;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName : RecordSheetQueryParam
 * @Description : 查询巡检记录表
 * <AUTHOR> jiangzixuan
 * @Date: 2022-10-10 14:11
 */
@Getter
@Setter
public class RecordSheetQueryParam {
    private List<BaseVo> nodes;
    private List<Long> templateIds;
    private List<Long> schemeId;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer cycle;
    /**
     * 0,横，1，纵
     */
    private Integer type;
    private List<DeviceFieldData> deviceParam;
    private Page page;
    private List<Long> techParamDataId;
    /**
     * true,合并导出，false，分sheet导出
     */
    private boolean merge;
}

