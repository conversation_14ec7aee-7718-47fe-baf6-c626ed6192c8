﻿package com.cet.eem.fusion.maintenance.core.service.inspection.impl;

import cn.hutool.core.util.ObjectUtil;
import com.cet.eem.bll.common.def.constant.TableName;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.InspectionParameter;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.InspectionScheme;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.InspectionSchemeDetail;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.InspectionSchemeWithSubLayer;
import com.cet.eem.bll.common.util.CharCheckUtils;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.constant.ParamType;
import com.cet.eem.fusion.maintenance.core.dao.InspectionParameterDao;
import com.cet.eem.fusion.maintenance.core.dao.InspectionSchemeDao;
import com.cet.eem.fusion.maintenance.core.dao.PlanSheetDao;
import com.cet.eem.fusion.maintenance.core.model.scheme.*;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectorSchemeService;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.common.constant.ExcelType;
import com.cet.eem.common.exception.ValidationException;
import com.cet.eem.fusion.common.utils.file.FileUtils;
import com.cet.electric.commons.ApiResult;

import com.cet.electric.commons.ApiResult;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.eem.common.utils.PoiExcelUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.model.base.ModelSingeWriteVo;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.service.EemCloudAuthService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.cet.eem.bll.maintenance.constant.TextConstant.*;
import com.cet.eem.fusion.common.def.common.ContentTypeDef;

/**
 * @ClassName : InspectorSchemeServiceImpl
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-14 15:51
 */
@Service
public class InspectorSchemeServiceImpl implements InspectorSchemeService {

    @Autowired
    private InspectionSchemeDao inspectionSchemeDao;

    @Autowired
    private InspectionParameterDao inspectionParameterDao;

    @Autowired
    private PlanSheetDao planSheetDao;

    @Autowired
    private EemCloudAuthService eemCloudAuthService;

    @Autowired
    private ModelServiceUtils modelServiceUtils;

    public static final List<String> PARAM_FIELD_LIST = new LinkedList<>();

    @Override
    public ApiResult<List<InspectionSchemeVo>> queryInspectionScheme(QueryInspectionSchemeRequest queryInspectionSchemeRequest) {
        LambdaQueryWrapper<InspectionScheme> queryWrapper = LambdaQueryWrapper.of(InspectionScheme.class);
        queryWrapper.eq(InspectionScheme::getProjectId, GlobalInfoUtils.getTenantId());
        if (StringUtils.isNotEmpty(queryInspectionSchemeRequest.getName())) {
            queryWrapper.like(InspectionScheme::getName, queryInspectionSchemeRequest.getName());
        }
        ApiResult<List<InspectionScheme>> listResultWithTotal = inspectionSchemeDao.selectPage(queryWrapper, queryInspectionSchemeRequest.getPage());
        ApiResult<List<InspectionSchemeVo>> result = new ApiResult<>();
        result.setTotal(listResultWithTotal.getTotal());
        List<InspectionScheme> data = listResultWithTotal.getData();
        if (CollectionUtils.isNotEmpty(data)) {
            List<InspectionSchemeVo> inspectionSchemeVoList = data.stream().map(InspectionSchemeVo::new).collect(Collectors.toList());
            result.setData(inspectionSchemeVoList);
            ApiResult<List<UserVo>> queryAllUserResult = eemCloudAuthService.getAllUsers(queryInspectionSchemeRequest.getTenantId());
            queryAllUserResult.throwExceptionIfFailed();
            List<UserVo> userVoList = queryAllUserResult.getData();
            if (CollectionUtils.isNotEmpty(userVoList)) {
                Map<Long, String> idNameMap = userVoList.stream().collect(Collectors.toMap(UserVo::getId, UserVo::getName));
                inspectionSchemeVoList.forEach(s -> {
                    if (Objects.nonNull(s.getCreateUser())) {
                        s.setUserName(idNameMap.get(s.getCreateUser()));
                    }
                });
            }
        }
        return result;
    }

    @Override
    public InspectionScheme addInspectionScheme(AddInspectionSchemeRequest addInspectionSchemeRequest) {
        if (!CharCheckUtils.checkStringValue(addInspectionSchemeRequest.getName())) {
            throw new IllegalArgumentException("输入的名称中包含特殊字符，请调整");
        }
        checkNameRepeat(addInspectionSchemeRequest.getName());
        InspectionSchemeWithSubLayer inspectionSchemeWithSubLayer = new InspectionSchemeWithSubLayer();
        inspectionSchemeWithSubLayer.setCreateTime(System.currentTimeMillis());
        inspectionSchemeWithSubLayer.setCreateUser(GlobalInfoUtils.getUserId());
        inspectionSchemeWithSubLayer.setName(addInspectionSchemeRequest.getName());
        inspectionSchemeWithSubLayer.setProjectId(GlobalInfoUtils.getTenantId());
        inspectionSchemeWithSubLayer.setSchemeDetails(addInspectionSchemeRequest.getSchemeDetails());
        inspectionSchemeDao.insert(inspectionSchemeWithSubLayer);
        return inspectionSchemeWithSubLayer;
    }

    @Override
    public QueryInspectionDetailResult queryInspectionSchemeAndDetail(Long inspectionSchemeId) {
        InspectionSchemeWithSubLayer inspectionSchemeWithSubLayer = inspectionSchemeDao.selectRelatedById(InspectionSchemeWithSubLayer.class, inspectionSchemeId);
        if (Objects.isNull(inspectionSchemeWithSubLayer)) {
            return null;
        }
        QueryInspectionDetailResult queryInspectionDetailResult = new QueryInspectionDetailResult();
        queryInspectionDetailResult.setName(inspectionSchemeWithSubLayer.getName());
        List<InspectionSchemeDetail> schemeDetails = inspectionSchemeWithSubLayer.getSchemeDetails();
        if (CollectionUtils.isNotEmpty(schemeDetails)) {
            Set<Long> inspectionParameterIds = schemeDetails.stream().map(InspectionSchemeDetail::getInspectionParameterId).filter(Objects::nonNull).collect(Collectors.toSet());
            List<InspectionParameter> inspectionParameters = inspectionParameterDao.selectBatchIds(inspectionParameterIds);
            Map<Long, String> idNameMap = inspectionParameters.stream().collect(Collectors.toMap(InspectionParameter::getId, InspectionParameter::getName));
            schemeDetails.forEach(s -> {
                if (Objects.nonNull(s.getInspectionParameterId())) {
                    s.setName(idNameMap.get(s.getInspectionParameterId()));
                }
            });
            List<InspectionSchemeDetail> statusQuantity = schemeDetails.stream().filter(s -> Objects.nonNull(s.getType()) && s.getType() == 1).collect(Collectors.toList());
            List<InspectionSchemeDetail> analogQuantity = schemeDetails.stream().filter(s -> Objects.nonNull(s.getType()) && s.getType() == 2).collect(Collectors.toList());
            List<InspectionSchemeDetail> textQuantity = schemeDetails.stream().filter(s -> Objects.nonNull(s.getType()) && s.getType() == 3).collect(Collectors.toList());
            queryInspectionDetailResult.setStatusQuantity(statusQuantity);
            queryInspectionDetailResult.setAnalogQuantity(analogQuantity);
            queryInspectionDetailResult.setTextQuantity(textQuantity);
        }
        return queryInspectionDetailResult;
    }

    @Override
    public void editInspectionScheme(EditInspectionSchemeRequest editInspectionSchemeRequest) {
        if (!CharCheckUtils.checkStringValue(editInspectionSchemeRequest.getName())) {
            throw new IllegalArgumentException("输入的名称中包含特殊字符，请调整");
        }
        InspectionSchemeWithSubLayer inspectionSchemeWithSubLayer = inspectionSchemeDao.selectRelatedById(InspectionSchemeWithSubLayer.class, editInspectionSchemeRequest.getId());
        if (Objects.isNull(inspectionSchemeWithSubLayer)) {
            return;
        }
        handleSchemeNameChange(inspectionSchemeWithSubLayer, editInspectionSchemeRequest);
        handleSchemeDetailAdd(inspectionSchemeWithSubLayer, editInspectionSchemeRequest);
        handleSchemeDetailDelete(inspectionSchemeWithSubLayer, editInspectionSchemeRequest);
    }

    @Override
    public void deleteInspectionScheme(Collection<Long> inspectionSchemeIds) {
        checkInspectionSchemeInUsed(inspectionSchemeIds);
        LambdaQueryWrapper<InspectionScheme> queryWrapper = LambdaQueryWrapper.of(InspectionScheme.class);
        queryWrapper.in(InspectionScheme::getId, inspectionSchemeIds);
        List<InspectionSchemeWithSubLayer> inspectionSchemeWithSubLayers = inspectionSchemeDao.selectRelatedList(InspectionSchemeWithSubLayer.class, queryWrapper);
        for (InspectionSchemeWithSubLayer inspectionSchemeWithSubLayer : inspectionSchemeWithSubLayers) {
            if (CollectionUtils.isNotEmpty(inspectionSchemeWithSubLayer.getSchemeDetails())) {
                inspectionSchemeDao.deleteChild(inspectionSchemeWithSubLayer.getId(), inspectionSchemeWithSubLayer.getSchemeDetails());
            }
            inspectionSchemeDao.deleteById(inspectionSchemeWithSubLayer.getId());
        }
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) {
        Workbook workbook = PoiExcelUtils.createWorkBook(ExcelType.BIG_DATA);
        String fileName = IMPORT_SCHEME_FILE_NAME_PREFIX
                + LocalDateTime.now().format(TimeUtil.SECONDTIMEFORMAT);
        Sheet sheet = workbook.createSheet(IMPORT_SCHEME_SHEET_NAME);
        sheet.setDefaultRowHeightInPoints(IMPORT_SCHEME_FIRST_COLUMN_WIDTH);
        PoiExcelUtils.setColumnWidth(0, 1, sheet, IMPORT_SCHEME_FIRST_COLUMN_WIDTH);
        PoiExcelUtils.setColumnWidth(1, 9, sheet, IMPORT_SCHEME_COLUMN_WIDTH);
        Font font = workbook.createFont();
        font.setFontName(FONT_MICROSOFT_YAHEI);
        font.setFontHeightInPoints((short) 11);
        //第一行合并单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));
        List<Row> rows = PoiExcelUtils.initRows(5, sheet, IMPORT_SCHEME_ROW_HEIGHT);
        CellStyle firstRowStyle = workbook.createCellStyle();
        firstRowStyle.setWrapText(true);
        firstRowStyle.setFont(font);
        firstRowStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //第一行，填写格式说明
        Row row1 = rows.get(0);
        Cell first = initCell(row1, 0, IMPORT_SCHEME_CAUTION, firstRowStyle);
        row1.setHeightInPoints(sheet.getDefaultRowHeightInPoints()
                * (first.getStringCellValue().split("\n").length));
        //设置内容样式
        CellStyle contentStyle = workbook.createCellStyle();
        contentStyle.setAlignment(HorizontalAlignment.CENTER);
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentStyle.setFont(font);
        initCell(rows.get(1), 0, SCHEME_NAME_TEXT, contentStyle);
        initCell(rows.get(2), 0, "1", contentStyle);
        initCell(rows.get(3), 0, "2", contentStyle);
        initCell(rows.get(4), 0, "3", contentStyle);
        initCell(rows.get(1), 1, SCHEME_NAME_DEMO, contentStyle);
        initCell(rows.get(2), 1, PARAM_TYPE_1_DEMO, contentStyle);
        initCell(rows.get(3), 1, PARAM_TYPE_2_DEMO, contentStyle);
        initCell(rows.get(4), 1, PARAM_TYPE_3_DEMO, contentStyle);
        // 写入导出连接关系表数据
        FileUtils.downloadExcel(response, workbook, fileName, ContentTypeDef.APPLICATION_MSEXCEL);
    }

    @Override
    public void importItem(MultipartFile file, Long projectId)
            throws IOException, InvalidFormatException, ValidationException {
        Workbook workbook = WorkbookFactory.create(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);
        Map<String, List<String>> schemeMap = parseSheetToMap(sheet);
        validateData(schemeMap, projectId);
        saveData(schemeMap, projectId);
    }

    Cell initCell(Row row, int columnIndex, String value, CellStyle cellStyle) {
        Cell cell = row.createCell(columnIndex);
        cell.setCellValue(value);
        cell.setCellStyle(cellStyle);
        return cell;
    }

    Map<String, List<String>> parseSheetToMap(Sheet sheet) {
        int sheetWidth = sheet.getRow(1).getLastCellNum();
        Map<String, List<String>> result = new LinkedHashMap<>();
        //按列读，第一列为表头，从第二列开始读取数据
        for (int columnNum = 1; columnNum < sheetWidth; columnNum++) {
            String scheme = sheet.getRow(1).getCell(columnNum).getStringCellValue();
            List<String> params = new ArrayList<>();
            //按行读，第三行开始读取参数名称
            for (int rowNum = 2; rowNum <= sheet.getLastRowNum(); rowNum++) {
                Row row = sheet.getRow(rowNum);
                Cell cell = row.getCell(columnNum);
                if (cell != null && StringUtils.isNotEmpty(cell.getStringCellValue())) {
                    params.add(cell.getStringCellValue().trim());
                }
            }
            if (result.containsKey(scheme)) {
                throw new ValidationException("第2行第" + columnNum + "列巡检方案名称重复:" + scheme);
            }
            result.put(scheme, params);
        }
        return result;
    }

    /**
     * 巡检方案导入内容校验
     *
     * @param schemeMap key-巡检方案名称 value-巡检参数列表
     * @param projectId
     */
    void validateData(Map<String, List<String>> schemeMap, Long projectId) {
        List<InspectionParameter> inspectionParameters =
                inspectionParameterDao.selectList(LambdaQueryWrapper.of(InspectionParameter.class)
                        .eq(InspectionParameter::getProjectId, projectId));
        Map<String, Integer> paramCache = inspectionParameters.stream()
                .collect(Collectors.toMap(InspectionParameter::getName, InspectionParameter::getType));
        AtomicInteger rowIndex = new AtomicInteger();
        AtomicInteger columnIndex = new AtomicInteger(2);
        schemeMap.forEach((scheme, params) -> {
            rowIndex.set(2);
            if (StringUtils.isEmpty(scheme) || scheme.split(" ").length == 0) {
                throw new ValidationException("校验失败,第" + rowIndex + "行第" + columnIndex + "列，校验方案名称不能为空");
            }
            rowIndex.getAndIncrement();
            for (String param : params) {
                int index = param.lastIndexOf("_");
                if (index != -1) {
                    String suffix = param.substring(index + 1);
                    param = param.substring(0, index);
                    //文本量
                    if ("T".equals(suffix)) {
                        if (paramCache.containsKey(param) && !paramCache.get(param).equals(ParamType.TEXT_PARAM.getCode())) {
                            throw new ValidationException("校验失败,第" + rowIndex + "行第" + columnIndex + "列，已存在同名不同类型参数:" + param);
                        }
                        paramCache.put(param, ParamType.TEXT_PARAM.getCode());
                        rowIndex.getAndIncrement();
                        continue;
                    }
                    //模拟量
                    String[] split = suffix.split("~");
                    String pattern = "^[+-]?\\d+(\\.\\d+)?$";
                    if (split.length == 2 && split[0].matches(pattern) && split[1].matches(pattern)) {
                        if (paramCache.containsKey(param) && !paramCache.get(param).equals(ParamType.SIMULATE_PARAM.getCode())) {
                            throw new ValidationException("校验失败,第" + rowIndex + "行第" + columnIndex + "列，已存在同名不同类型参数:" + param);
                        }
                        if (Double.parseDouble(split[0]) <= Double.parseDouble(split[1])) {
                            throw new ValidationException("校验不通过，第" + rowIndex + "行第" + columnIndex + "列模拟量阈值错误，上限等于或低于下限");
                        }
                        paramCache.put(param, ParamType.SIMULATE_PARAM.getCode());
                        rowIndex.getAndIncrement();
                        continue;
                    }
                }
                //其他情况均为状态量
                if (paramCache.containsKey(param) && !paramCache.get(param).equals(ParamType.STATUS_PARAM.getCode())) {
                    throw new ValidationException("校验失败,第" + rowIndex + "行第" + columnIndex + "列，已存在同名不同类型参数:" + param);
                }
                paramCache.put(param, ParamType.STATUS_PARAM.getCode());
                rowIndex.getAndIncrement();
            }
            rowIndex.set(3);
            columnIndex.getAndIncrement();
        });
    }

    void saveData(Map<String, List<String>> schemeMap, Long projectId) {
        //查询数据库，准备比对数据
        List<InspectionScheme> inspectionSchemes =
                inspectionSchemeDao.selectList(LambdaQueryWrapper.of(InspectionScheme.class)
                        .eq(InspectionScheme::getProjectId, projectId));
        Map<String, InspectionScheme> schemeCache = inspectionSchemes.stream()
                .collect(Collectors.toMap(InspectionScheme::getName, inspectionScheme -> {
                    return inspectionScheme;
                }));
        List<List<Object>> paramDataList = new LinkedList<>();
        for (Map.Entry<String, List<String>> entry : schemeMap.entrySet()) {
            String scheme = entry.getKey();
            List<String> params = entry.getValue();
            InspectionScheme inspectionScheme;
            List<InspectionParameter> inspectionParameters =
                    inspectionParameterDao.selectList(LambdaQueryWrapper.of(InspectionParameter.class)
                            .eq(InspectionParameter::getProjectId, projectId));
            Map<String, InspectionParameter> paramCache = inspectionParameters.stream()
                    .collect(Collectors.toMap(InspectionParameter::getName, inspectionParameter -> {
                        return inspectionParameter;
                    }));
            List<InspectionSchemeDetail> newInspectionSchemeDetails = new LinkedList<>();
            List<InspectionSchemeDetail> UpdateInspectionSchemeDetails = new LinkedList<>();
            List<InspectionSchemeDetail> existInspectionSchemeDetails = new LinkedList<>();
            //保存scheme信息
            if (!schemeCache.containsKey(scheme)) {
                inspectionScheme = new InspectionScheme();
                inspectionScheme.setProjectId(projectId);
                inspectionScheme.setName(scheme);
                inspectionScheme.setCreateTime(System.currentTimeMillis());
                inspectionScheme.setCreateUser(GlobalInfoUtils.getUserId());
                Long schemeId = inspectionSchemeDao.insert(inspectionScheme);
                inspectionScheme.setId(schemeId);
            } else {
                inspectionScheme = schemeCache.get(scheme);
                existInspectionSchemeDetails =
                        queryInspectionSchemeAndDetail(inspectionScheme.getId()).getAllInspectionSchemeDetails();
            }
            //保存param信息，顺便封装schemedetail数据
            for (String param : params) {
                InspectionSchemeDetail inspectionSchemeDetail = new InspectionSchemeDetail();
                int index = param.lastIndexOf("_");
                String paramName;
                boolean isNewParam;
                if (index != -1) {
                    String suffix = param.substring(index + 1);
                    paramName = param.substring(0, index);
                    String[] split = suffix.split("~");
                    String pattern = "^[+-]?\\d+(\\.\\d+)?$";
                    if (index != -1 && "T".equals(suffix)) {
                        //文本量
                        isNewParam = addParamToDataList(paramName, projectId, ParamType.TEXT_PARAM.getCode(), paramDataList, paramCache);
                        inspectionSchemeDetail.setType(ParamType.TEXT_PARAM.getCode());
                    } else if (split.length == 2 && split[0].matches(pattern) && split[1].matches(pattern)) {
                        //模拟量
                        isNewParam = addParamToDataList(paramName, projectId, ParamType.SIMULATE_PARAM.getCode(), paramDataList, paramCache);
                        inspectionSchemeDetail.setType(ParamType.SIMULATE_PARAM.getCode());
                        inspectionSchemeDetail.setMax(Double.valueOf(split[0]));
                        inspectionSchemeDetail.setMin(Double.valueOf(split[1]));
                    } else {
                        paramName = param;
                        //状态量
                        isNewParam = addParamToDataList(paramName, projectId, ParamType.STATUS_PARAM.getCode(), paramDataList, paramCache);
                        inspectionSchemeDetail.setType(ParamType.STATUS_PARAM.getCode());
                    }
                } else {
                    paramName = param;
                    //状态量
                    isNewParam = addParamToDataList(paramName, projectId, ParamType.STATUS_PARAM.getCode(), paramDataList, paramCache);
                    inspectionSchemeDetail.setType(ParamType.STATUS_PARAM.getCode());
                }
                inspectionSchemeDetail.setParaName(paramName);
                inspectionSchemeDetail.setSort(1);
                inspectionSchemeDetail.setInspectionParameterId(paramCache.get(paramName).getId());
                if (isNewParam) {
                    newInspectionSchemeDetails.add(inspectionSchemeDetail);
                } else {
                    UpdateInspectionSchemeDetails.add(inspectionSchemeDetail);
                }
            }
            //批量保存param数据
            ModelSingeWriteVo modelSingeWriteVo = modelServiceUtils.writeDataBatch(TableName.INSPECTION_PARAMETER, true, PARAM_FIELD_LIST, paramDataList, null);
            paramDataList.clear();
            //整理新插入的param的schemedetiallist
            if (CollectionUtils.isNotEmpty(newInspectionSchemeDetails)) {
                for (int i = 0; i < newInspectionSchemeDetails.size(); i++) {
                    newInspectionSchemeDetails.get(i).setInspectionParameterId(Long.valueOf(modelSingeWriteVo.getFilterData().get(i).get(0).toString()));
                    newInspectionSchemeDetails.get(i).combine(existInspectionSchemeDetails);
                }
            }
            if (CollectionUtils.isNotEmpty(UpdateInspectionSchemeDetails)) {
                for (InspectionSchemeDetail inspectionSchemeDetail : UpdateInspectionSchemeDetails) {
                    inspectionSchemeDetail.combine(existInspectionSchemeDetails);
                }
            }
            //保存schemedetail信息
            saveSchemeDetail(inspectionScheme, existInspectionSchemeDetails);
        }
    }

    boolean addParamToDataList(String paramName, Long projectId, Integer type,
                               List<List<Object>> writeDataList, Map<String, InspectionParameter> paramCache) {

        if (!paramCache.containsKey(paramName)) {
            if (PARAM_FIELD_LIST.size() == 0) {
                PARAM_FIELD_LIST.add("name");
                PARAM_FIELD_LIST.add("type");
                PARAM_FIELD_LIST.add("projectid");
            }
            List<Object> values = new LinkedList<>();
            values.add(paramName);
            values.add(type);
            values.add(projectId);
            writeDataList.add(values);
            paramCache.put(paramName, new InspectionParameter());
        }
        if (paramCache.containsKey(paramName) && ObjectUtil.isNotEmpty(paramCache.get(paramName).getId())) {
            return false;
        } else {
            return true;
        }
    }

    void saveSchemeDetail(InspectionScheme inspectionScheme, List<InspectionSchemeDetail> inspectionSchemeDetailList) {
        EditInspectionSchemeRequest editInspectionSchemeRequest = new EditInspectionSchemeRequest();
        editInspectionSchemeRequest.setId(inspectionScheme.getId());
        editInspectionSchemeRequest.setName(inspectionScheme.getName());
        editInspectionSchemeRequest.setSchemeDetails(inspectionSchemeDetailList);
        editInspectionScheme(editInspectionSchemeRequest);
    }

    private void checkInspectionSchemeInUsed(Collection<Long> inspectionSchemeIds) {
        LambdaQueryWrapper<PlanSheet> queryWrapper = LambdaQueryWrapper.of(PlanSheet.class);
        queryWrapper.in(PlanSheet::getInspectionSchemeId, inspectionSchemeIds);
        List<PlanSheet> planSheetList = planSheetDao.selectList(queryWrapper);
        Assert.isTrue(CollectionUtils.isEmpty(planSheetList), String.format("巡检方案已被巡检计划%s使用", planSheetList.stream().map(PlanSheet::getName).collect(Collectors.joining(","))));
    }

    private void handleSchemeDetailDelete(InspectionSchemeWithSubLayer inspectionSchemeWithSubLayer, EditInspectionSchemeRequest editInspectionSchemeRequest) {
        if (CollectionUtils.isEmpty(inspectionSchemeWithSubLayer.getSchemeDetails())) {
            return;
        }
        List<Long> schemeDetailIds = Objects.isNull(editInspectionSchemeRequest.getSchemeDetails()) ? Collections.emptyList() : editInspectionSchemeRequest.getSchemeDetails().stream().map(InspectionSchemeDetail::getId).filter(Objects::nonNull).collect(Collectors.toList());
        List<InspectionSchemeDetail> waitDeleteSchemeDetail = inspectionSchemeWithSubLayer.getSchemeDetails().stream().filter(s -> {
            if (CollectionUtils.isEmpty(schemeDetailIds)) {
                return true;
            }
            if (!schemeDetailIds.contains(s.getId())) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(waitDeleteSchemeDetail)) {
            inspectionSchemeDao.deleteChild(editInspectionSchemeRequest.getId(), waitDeleteSchemeDetail);
        }
    }

    private void handleSchemeDetailAdd(InspectionSchemeWithSubLayer inspectionSchemeWithSubLayer, EditInspectionSchemeRequest editInspectionSchemeRequest) {
        if (CollectionUtils.isEmpty(editInspectionSchemeRequest.getSchemeDetails())) {
            return;
        }
        List<Long> schemeDetailIds = Objects.isNull(inspectionSchemeWithSubLayer.getSchemeDetails()) ? Collections.emptyList() : inspectionSchemeWithSubLayer.getSchemeDetails().stream().map(InspectionSchemeDetail::getId).collect(Collectors.toList());
        List<InspectionSchemeDetail> waitAddSchemeDetail = editInspectionSchemeRequest.getSchemeDetails().stream().filter(s -> {
            if (Objects.isNull(s.getId()) || CollectionUtils.isEmpty(schemeDetailIds)) {
                return true;
            }
            if (!schemeDetailIds.contains(s.getId())) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(waitAddSchemeDetail)) {
            inspectionSchemeDao.insertChild(editInspectionSchemeRequest.getId(), waitAddSchemeDetail);
        }
    }

    private void handleSchemeNameChange(InspectionSchemeWithSubLayer inspectionSchemeWithSubLayer, EditInspectionSchemeRequest editInspectionSchemeRequest) {
        if (inspectionSchemeWithSubLayer.getName().equals(editInspectionSchemeRequest.getName())) {
            return;
        }
        checkNameRepeat(editInspectionSchemeRequest.getName());
        InspectionScheme inspectionScheme = new InspectionScheme();
        inspectionScheme.setId(editInspectionSchemeRequest.getId());
        inspectionScheme.setName(editInspectionSchemeRequest.getName());
        inspectionScheme.setProjectId(GlobalInfoUtils.getTenantId());
        inspectionScheme.setCreateTime(inspectionSchemeWithSubLayer.getCreateTime());
        inspectionScheme.setCreateUser(inspectionSchemeWithSubLayer.getCreateUser());
        inspectionSchemeDao.updateById(inspectionScheme);
    }

    private void checkNameRepeat(String name) {
        LambdaQueryWrapper<InspectionScheme> queryWrapper = LambdaQueryWrapper.of(InspectionScheme.class);
        queryWrapper.eq(InspectionScheme::getProjectId, GlobalInfoUtils.getTenantId());
        queryWrapper.eq(InspectionScheme::getName, name);
        InspectionScheme inspectionScheme = inspectionSchemeDao.selectOne(queryWrapper);
        Assert.isNull(inspectionScheme, "巡检方案名称重复");
    }
}


