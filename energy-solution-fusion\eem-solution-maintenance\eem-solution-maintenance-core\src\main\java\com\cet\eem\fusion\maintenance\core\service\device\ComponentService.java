﻿package com.cet.eem.fusion.maintenance.core.service.device;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.DeviceComponent;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.component.AddDeviceComponent;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.component.EditDeviceComponent;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.component.QueryNodeComponentDto;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.Map;

/**
 * @Author: jzx
 * @Description:
 * @Data: Created in 2021-05-11
 */
public interface ComponentService {
    /**
     * 根据设备查询零件信息
     * @param dto
     * @return
     */
    List<DeviceComponent> queryComponent(QueryNodeComponentDto dto);

    /**
     * 批量删除零件信息
     * @param ids
     */
    void delecteComponent(List<Long> ids);

    /**
     * 新增零件信息
     * @param addDeviceComponent
     * @return
     */
    DeviceComponent addComponent(AddDeviceComponent addDeviceComponent);

    /**
     * 编辑零件信息
     * @param editDeviceComponent
     * @return
     */
    DeviceComponent editComponent(EditDeviceComponent editDeviceComponent);

    /**
     * 从文件导入数据到零件库
     * @param multipartFile
     * @throws IOException
     */
    void importComponent(MultipartFile multipartFile) throws IOException;

    /**
     * 查询数据中设备、备件、零件的型号信息
     * @param keyword
     * @return
     */
    Set<String> queryModelList(String keyword);

    List<Map<String, Object>> queryDeviceTree(Long projectId);
    /**
     * 根据选择的节点去选择导出的数据
     * @param response
     * @param id
     * @param modelLabel
     * @param type
     * @throws Exception
     */
    void exportDeviceComponentByDeviceType(HttpServletResponse response,Long id,String modelLabel,Integer type);
}

