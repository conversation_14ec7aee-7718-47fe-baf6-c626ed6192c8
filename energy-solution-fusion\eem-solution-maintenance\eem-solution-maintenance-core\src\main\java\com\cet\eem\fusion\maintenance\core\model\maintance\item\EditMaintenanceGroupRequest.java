﻿package com.cet.eem.fusion.maintenance.core.model.maintance.item;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @ClassName : EditMiantenanceGroupRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-10 14:14
 */
@Getter
@Setter
@ApiModel(value = "EditMaintenanceGroupRequest", description = "新增维保项目组")
public class EditMaintenanceGroupRequest {
    /**
     * 维保项目组名
     */
    @NotNull(message = "维保项目组id不能为空")
    private Long id;

    /**
     * 维保项目名称
     */
    @NotEmpty(message = "维保项目组名不能为空")
    private String name;
}

