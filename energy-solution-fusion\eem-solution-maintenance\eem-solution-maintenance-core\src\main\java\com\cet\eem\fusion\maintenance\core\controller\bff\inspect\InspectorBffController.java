﻿package com.cet.eem.fusion.maintenance.core.controller.bff.inspect;

import com.cet.eem.bll.common.log.annotation.OperationLog;
import com.cet.eem.bll.common.log.constant.EEMOperationLogType;
import com.cet.eem.bll.common.log.constant.EnumOperationSubType;
import com.cet.eem.bll.common.log.service.CommonUtilsService;
import com.cet.eem.fusion.maintenance.core.model.inspector.*;
import com.cet.eem.fusion.maintenance.core.service.bff.inspect.InspectorBffService;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectorService;
import com.cet.electric.commons.ApiResult;

import com.cet.electric.commons.ApiResult;
import com.cet.eem.common.model.auth.user.RoleVo;
import com.cet.eem.common.model.auth.user.UserGroupVo;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.eem.common.utils.AesUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * @ClassName : InspectorBffController
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-03-12 16:38
 */

public class InspectorBffController {

    @Autowired
    private InspectorService inspectorService;

    @Autowired
    private InspectorBffService inspectorBffService;

    @Autowired
    private CommonUtilsService commonUtilsService;

    @ApiOperation(value = "巡检人员查询")
    @PostMapping("/user")
    public ApiResult<List<UserVo>> queryInspector(@Valid @RequestBody QueryInspectorRequest queryInspectorRequest) {
        ApiResult<List<UserVo>> listResultWithTotal = inspectorService.queryMaintenanceUser(queryInspectorRequest);
        encrypt(listResultWithTotal.getData());
        return listResultWithTotal;
    }

    private void encrypt(List<UserVo> users) {
        if (CollectionUtils.isEmpty(users)) {
            return;
        }

        users.forEach(it -> {
            it.setName(AesUtils.encryptLoginKey(it.getName()));
            it.setNicName(AesUtils.encryptLoginKey(it.getNicName()));
            it.setMobilePhone(AesUtils.encryptLoginKey(it.getMobilePhone()));
            it.setEmail(AesUtils.encryptLoginKey(it.getEmail()));
        });
    }

    @ApiOperation(value = "巡检人员编辑")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR, subType = EnumOperationSubType.UPDATE, description = "【巡检人员编辑】")
    @PatchMapping("/user")
    public ApiResult<Void> editInspectorUser(@Valid @RequestBody EditInspectorUserRequest editInspectorUserRequest) {
        inspectorService.editInspectorUser(editInspectorUserRequest);
        return ApiResult.ok();
    }

    @ApiOperation(value = "巡检人员删除")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR, subType = EnumOperationSubType.DELETE, description = "【巡检人员删除】")
    @DeleteMapping("/user")
    public ApiResult<Void> deleteInspectorUser(@RequestParam("id") Long id, @RequestParam("name") String name, @RequestParam("tenantId") Long tenantId) {
        inspectorService.deleteInspectorUser(id, name, tenantId);
        return ApiResult.ok();
    }

    @ApiOperation(value = "判断是否为巡检人员")
    @GetMapping("/check")
    public ApiResult<Boolean> isInspectorUser() {
        return ApiResult.ok(inspectorService.isInspectorUserByUserId());
    }

    @ApiOperation(value = "查询巡检人员同组人员")
    @GetMapping("/group/users")
    public ApiResult<List<UserVo>> querySameGroupMember() {
        return ApiResult.ok(inspectorService.querySameGroupMember());
    }

    @ApiOperation(value = "巡检角色查询")
    @GetMapping("/roles")
    public ApiResult<List<RoleVo>> queryInspectorRoles(@RequestParam Long tenantId) {
        return ApiResult.ok(inspectorService.queryInspectorRoles(tenantId));
    }

    /**
     * @param inspectorRegistryRequest
     * @return
     * @deprecated
     */
    @ApiOperation(value = "巡检人员注册")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR, subType = EnumOperationSubType.ADD, description = "【巡检人员注册】")
    @PostMapping("/register")
    @Deprecated
    public ApiResult<UserVo> inspectorRegistryRequest(@Valid @RequestBody InspectorRegistryRequest inspectorRegistryRequest) {
        UserVo userVo = inspectorService.inspectorRegistry(inspectorRegistryRequest);
        return ApiResult.ok(userVo);
    }

    @ApiOperation(value = "巡检人员注册")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR, subType = EnumOperationSubType.ADD, description = "【巡检人员注册】")
    @PostMapping("/register/security")
    public ApiResult<UserVo> inspectorRegistrySecurity(@Valid @RequestBody InspectorRegistryRequest inspectorRegistryRequest) {
        UserVo userVo = inspectorService.inspectorRegistrySecurity(inspectorRegistryRequest);
        return ApiResult.ok(userVo);
    }

    /**
     * @param inspectorPasswordChangeRequest
     * @return
     * @deprecated
     */
    @ApiOperation(value = "巡检人员密码修改")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR, subType = EnumOperationSubType.UPDATE, description = "【巡检人员密码修改】")
    @PatchMapping("/password")
    @Deprecated
    public ApiResult<Void> inspectorPasswordChange(@Valid @RequestBody InspectorPasswordChangeRequest inspectorPasswordChangeRequest) {
        inspectorService.inspectorPasswordChange(inspectorPasswordChangeRequest);
        return ApiResult.ok();
    }

    @ApiOperation(value = "修改巡检人员密码，密码需要进行加密")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR, subType = EnumOperationSubType.UPDATE, description = "【巡检人员密码修改】")
    @PatchMapping("/password/security")
    public ApiResult<Void> inspectorPasswordChangeSecurity(@Valid @RequestBody InspectorPasswordChangeRequest inspectorPasswordChangeRequest) {
        inspectorService.inspectorPasswordChangeSecurity(inspectorPasswordChangeRequest);
        return ApiResult.ok();
    }

    @ApiOperation(value = "查询巡检角色人员关联信息")
    @PostMapping("/roles")
    public ApiResult<QueryInspectorRolesResult> queryCurrentProjectSignInGroup(@RequestBody QueryInspectorRolesRequest queryInspectorRolesRequest) {
        return ApiResult.ok(inspectorService.queryInspectorRoleInfo(queryInspectorRolesRequest));
    }

    @ApiOperation(value = "查询班组")
    @GetMapping("/team")
    public ApiResult<List<UserGroupVo>> queryInspectorTeam(@RequestParam Long tenantId) {
        return ApiResult.ok(inspectorBffService.queryInspectorTeam(tenantId));
    }

    @ApiOperation(value = "查询班组")
    @GetMapping("/teamWithOutUser")
    public ApiResult<List<UserGroupVo>> queryInspectorTeamWithoutUser(
            @RequestParam Long tenantId,
            @RequestParam(required = false) @ApiParam(name = "name", value = "班组名") String name) {
        return ApiResult.ok(inspectorBffService.queryInspectorTeamWithoutUser(tenantId, name));
    }

    @ApiOperation(value = "查询班组，不限制班组")
    @GetMapping("/teamWithOutUser/noAuth")
    public ApiResult<List<UserGroupVo>> queryInspectorTeamWithOutAuth(
            @RequestParam Long tenantId,
            @RequestParam(required = false) @ApiParam(name = "name", value = "班组名") String name) {
        return ApiResult.ok(inspectorBffService.queryInspectorTeamWithOutAuth(tenantId, name));
    }

    @ApiOperation(value = "查询班组成员")
    @GetMapping("/inspectorUser")
    public ApiResult<List<UserVo>> queryInspectorUser(@RequestParam Long groupId) {
        return ApiResult.ok(inspectorService.queryMaintenanceUser(groupId));
    }

    @ApiOperation(value = "新增班组")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR_TEAM, subType = EnumOperationSubType.ADD, description = "【新增班组】")
    @PostMapping("/team")
    public ApiResult<Long> addInspectorTeam(@Valid @RequestBody AddInspectorTeamRequest addInspectorTeamRequest) {
        Long teamId = inspectorService.addInspectorTeam(addInspectorTeamRequest);
        return ApiResult.ok(teamId);
    }

    @ApiOperation(value = "编辑班组")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR_TEAM, subType = EnumOperationSubType.UPDATE, description = "【编辑班组】")
    @PatchMapping("/team")
    public ApiResult<Void> editInspectorTeam(@Valid @RequestBody EditInspectorTeamRequest editInspectorTeamRequest) {
        inspectorService.editInspectorTeam(editInspectorTeamRequest);
        return ApiResult.ok();
    }

    @ApiOperation(value = "删除班组")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR_TEAM, subType = EnumOperationSubType.DELETE, description = "【删除班组】")
    @DeleteMapping("/team")
    public ApiResult<Void> deleteInspectorTeam(@RequestParam("id") Long id, @RequestParam("name") String name, @RequestParam("tenantId") Long tenantId) {
        inspectorService.deleteInspectorTeam(id, name, tenantId);
        return ApiResult.ok();
    }

    /**
     * @param inspectorPasswordChangeRequest
     * @return
     * @deprecated
     */
    @ApiOperation(value = "确认用密码修改")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR, subType = EnumOperationSubType.UPDATE, description = "【巡检人员密码修改】")
    @PatchMapping("/checkPassword")
    @Deprecated
    public ApiResult<Void> checkPasswordChange(@Valid @RequestBody InspectorPasswordChangeRequest inspectorPasswordChangeRequest) {
        inspectorService.checkPasswordChange(inspectorPasswordChangeRequest);
        return ApiResult.ok();
    }

    @ApiOperation(value = "修改确认用密码，密码需要进行加密")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR, subType = EnumOperationSubType.UPDATE, description = "【巡检人员密码修改】")
    @PatchMapping("/checkPassword/security")
    public ApiResult<Void> checkPasswordChangeSecurity(@Valid @RequestBody InspectorPasswordChangeRequest inspectorPasswordChangeRequest) {
        inspectorService.checkPasswordChangeSecurity(inspectorPasswordChangeRequest);
        return ApiResult.ok();
    }

    @ApiOperation(value = "校验巡检用户口令")
    @PostMapping("/checkPassword")
    public ApiResult<Boolean> checkPassword(@RequestBody InspectorPasswordChangeRequest inspectorPasswordChangeRequest) {
        boolean result = inspectorService.checkPassword(inspectorPasswordChangeRequest);
        return ApiResult.ok(result);
    }

    @ApiOperation(value = "校验巡检用户口令，MD5加密之后")
    @PostMapping("/checkPassword/security")
    public ApiResult<Boolean> checkPassword(@RequestBody InspectorPasswordCheckRequest inspectorPasswordChangeRequest) {
        boolean result = inspectorService.checkPasswordSecurity(inspectorPasswordChangeRequest);
        return ApiResult.ok(result);
    }

    @ApiOperation(value = "下载巡检用户导入模板")
    @PostMapping("/download/Template")
    public ApiResult<Boolean> downloadTemplate(HttpServletResponse response) {
        inspectorService.downloadTemplate(response);
        return ApiResult.ok();
    }

    @ApiOperation(value = "导入巡检用户")
    @PostMapping("/importItem")
    public ApiResult<Boolean> importItem(@RequestBody MultipartFile file, HttpServletRequest request,@RequestParam Long tenantId) throws IOException {
        commonUtilsService.writeOperationLogs(EEMOperationLogType.INSPECTOR,EnumOperationSubType.UPDATE.getId(),"【导入巡检人员】",null);
        inspectorService.importItem(file, tenantId);
        return ApiResult.ok();
    }
}


