﻿package com.cet.eem.fusion.maintenance.core.model.sign;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @ClassName : EditSignInGroupRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-03-12 14:24
 */
@Getter
@Setter
@ApiModel(value = "EditSignInGroupRequest", description = "编辑签到组")
public class EditSignInGroupRequest {
    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Long id;
    /**
     * 分组名
     */
    @NotEmpty(message = "分组名不能为空")
    private String name;
}

