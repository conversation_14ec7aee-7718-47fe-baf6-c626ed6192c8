﻿package com.cet.eem.fusion.maintenance.core.model.maintance.item;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @ClassName : AddMaintenanceItemRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-10 14:42
 */
@Getter
@Setter
@ApiModel(value = "AddMaintenanceItemRequest", description = "新增维保项")
public class AddMaintenanceItemRequest {

    /**
     * 维保分组id
     */
    @NotNull(message = "维保项目组id不能为空")
    private Long maintenanceGroupId;

    /**
     * 维保方式
     */
    @NotNull(message = "维保方式不能为空")
    @JsonProperty("maintenancetype")
    private Long maintenanceType;

    /**
     * 维保内容
     */
    private String content;

    /**
     * 零部件id
     */
    private Long sparePartId;

    /**
     * 零部件数量
     */
    private Double number;

}
