﻿package com.cet.eem.fusion.maintenance.core.dao.impl;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.WorkOrderCheckInfo;
import com.cet.eem.fusion.maintenance.core.dao.WorkOrderCheckInfoDao;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderCheckInfoVo;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/5/27
 */
@Service
public class WorkOrderCheckInfoDaoImpl extends ModelDaoImpl<WorkOrderCheckInfo> implements WorkOrderCheckInfoDao {
    @Override
    @SuppressWarnings({"unchecked", "varargs"})
    public WorkOrderCheckInfoVo queryLastCheckInfo(String code, String taskId) {
        LambdaQueryWrapper<WorkOrderCheckInfo> wrapper = LambdaQueryWrapper.of(WorkOrderCheckInfo.class)
                .eq(WorkOrderCheckInfo::getCode, code)
                .eq(WorkOrderCheckInfo::getTaskId, taskId)
                .orderByDesc(WorkOrderCheckInfo::getCreateTime);

        Page page = new Page(0, 1);

        ApiResult<List<Map<String, Object>>> maps = this.selectMapsPage(wrapper, page);
        List<WorkOrderCheckInfoVo> infos = JsonTransferUtils.transferList(maps.getData(), WorkOrderCheckInfoVo.class);
        if (CollectionUtils.isEmpty(infos)) {
            return null;
        }

        return infos.get(0);
    }
}


