﻿package com.cet.eem.fusion.maintenance.core.model.devicemanage.template;

import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-04-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentNodeTreeVo extends EquipmentNodeTreeDto {
    private List<EquipmentNodeTreeVo> children;


    public EquipmentNodeTreeVo(String name, Long parentId, Long projectId, List<EquipmentNodeTreeVo> children) {
        super(name, parentId, projectId);
        this.children = children;
    }


}


