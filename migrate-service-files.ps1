# Maintenance Plugin Service Files Migration Script
param(
    [string]$SourcePath = "energy-base\cet-eem-maintenance\cet-eem-maintenanceservice\src\main\java\com\cet\eem\maintenanceservice",
    [string]$TargetPath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-service\src\main\java\com\cet\eem\fusion\config\server"
)

Write-Host "Starting maintenance plugin service files migration..." -ForegroundColor Green
Write-Host "Source path: $SourcePath" -ForegroundColor Yellow
Write-Host "Target path: $TargetPath" -ForegroundColor Yellow

# Get all Java files
$javaFiles = Get-ChildItem -Path $SourcePath -Filter "*.java" -Recurse

Write-Host "Found $($javaFiles.Count) Java files to migrate" -ForegroundColor Cyan

$migratedCount = 0
$errorCount = 0

foreach ($file in $javaFiles) {
    try {
        # Calculate relative path
        $relativePath = $file.FullName.Substring((Resolve-Path $SourcePath).Path.Length + 1)
        $targetFile = Join-Path $TargetPath $relativePath
        
        # Create target directory
        $targetDir = Split-Path $targetFile -Parent
        if (!(Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        }
        
        # Read source file content
        $content = Get-Content $file.FullName -Raw -Encoding UTF8
        
        # Update package declaration for service files
        $content = $content -replace "package com\.cet\.eem\.maintenanceservice", "package com.cet.eem.fusion.config.server"
        
        # Update import statements for maintenance module references
        $content = $content -replace "import com\.cet\.eem\.bll\.maintenance\.", "import com.cet.eem.fusion.maintenance.core."
        $content = $content -replace "import com\.cet\.eem\.maintenanceservice\.", "import com.cet.eem.fusion.config.server."
        
        # Update request mapping paths to use the new plugin prefix
        $content = $content -replace '@RequestMapping\("([^"]*)"', '@RequestMapping("/eem/solution/maintenance$1"'
        $content = $content -replace '@RequestMapping\(value\s*=\s*"([^"]*)"', '@RequestMapping(value = "/eem/solution/maintenance$1"'
        
        # Write to target file
        Set-Content -Path $targetFile -Value $content -Encoding UTF8
        
        $migratedCount++
        Write-Host "✓ Migrated: $relativePath" -ForegroundColor Green
        
    } catch {
        $errorCount++
        Write-Host "✗ Error: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nMigration completed!" -ForegroundColor Green
Write-Host "Successfully migrated: $migratedCount files" -ForegroundColor Cyan
Write-Host "Errors: $errorCount files" -ForegroundColor Red
