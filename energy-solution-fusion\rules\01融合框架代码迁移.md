你需要获取到插件的中文名称，插件的英文名称，插件的接口前缀。如果未能够获取到这三个信息，则直接退出执行，告诉用户补充信息，如果获取到所有的信息。

迁移时请满足如下两点要求：
1、输出原功能模块的所有java文件清单，针对这些所有的文件进行迁移，针对复杂的serviceimpl实现，dao层，也要迁移，不要忽略任何文件和内容
2、你可以在执行的时候创建md文件存储你的所有需要完成任务，并在任务执行阶段，不断更新md文件的任务状态，可以保留最新的时间信息（精确到分钟）
3、注意：迁移代码的时候不要修改代码的任何业务逻辑，保留原有的代码处理方式
4、新的架构需要满足如下的所有的迁移规范

## 融合框架插件开发规范

### 0. 插件依赖规范

所有插件必须依赖 `eem-solution-common` 模块，该模块提供了插件开发所需的基础功能和通用配置。

#### 0.1 依赖结构说明
插件必须位于eem-solution-fusion目录下
```
eem-solution-{plugin-name}/                    # 插件根目录
├── eem-solution-{plugin-name}-core/           # 核心模块
│   └── pom.xml                               # 依赖 eem-solution-common
└── eem-solution-{plugin-name}-service/        # 插件启动类  
    └── pom.xml                               # 依赖对应的 core 模块
```

#### 0.2 核心模块依赖配置

核心模块的 `pom.xml` 必须包含以下依赖：

```xml
<dependencies>
    <dependency>
        <groupId>com.cet.electric</groupId>
        <artifactId>eem-solution-common</artifactId>
        <version>4.0.0-SNAPSHOT</version>
    </dependency>
</dependencies>
```

#### 0.3 服务模块依赖配置

服务模块的 `pom.xml` 必须包含以下依赖：

```xml
<dependencies>
    <dependency>
        <groupId>com.cet.electric</groupId>
        <artifactId>eem-solution-{plugin-name}-core</artifactId>
        <version>4.0.0-SNAPSHOT</version>
    </dependency>
</dependencies>
```

#### 0.4 eem-solution-common 提供的功能

- **插件常量定义**：`PluginInfoDef.java` 包含所有插件的常量定义，所有新的插件必须在com.cet.eem.solution.common.def.common.PluginInfoDef进行注册。
- **通用配置类**：提供插件开发所需的基础配置
- **融合框架集成**：包含融合框架相关的依赖和配置
- **公共工具类**：提供插件开发常用的工具方法

#### 0.5 依赖版本管理

- 所有插件模块版本统一使用 `4.0.0-SNAPSHOT`
- `eem-solution-common` 版本与插件版本保持一致
- 父级 POM 使用 `matterhorn-basic-service-parent` 版本 `0.0.13`

### 1. 项目结构规范

插件项目应采用以下标准结构：
```
eem-solution-{plugin-name}/
├── pom.xml                                    # 主模块POM文件
├── eem-solution-{plugin-name}-core/           # 核心模块
│   ├── pom.xml                               # 核心模块POM文件
│   └── src/
│       ├── main/
│       │   ├── java/com/cet/eem/fusion/{plugin-name}/core/
│       │   │   ├── {PluginName}ConfigAutoConfiguration.java  # 自动配置类
│       │   │   ├── common/                   # 公共类目录
│       │   │   │   └── enums/                # 枚举类
│       │   │   ├── config/                   # 配置类目录
│       │   │   │   ├── PluginConfiguration.java           # 插件路由配置
│       │   │   │   ├── EemFusion{PluginName}BeanNameGenerator.java  # Bean名称生成器
│       │   │   │   └── EemSolution{PluginName}SwaggerConfig.java    # Swagger配置
│       │   │   ├── consumer/                 # 消息队列消费者目录
│       │   │   │   └── {Module}Consumer.java # 消息消费者类
│       │   │   ├── controller/               # 控制器目录
│       │   │   │   └── {Module}Controller.java            # 具体控制器类
│       │   │   ├── def/                      # 常量定义目录
│       │   │   │   └── {Plugin}ConstantDef.java           # 插件常量定义
│       │   │   ├── entity/                   # 实体类目录
│       │   │   │   ├── bo/                   # 业务对象目录
│       │   │   │   │   ├── {Business}BO.java # 业务对象类
│       │   │   │   │   └── {Other}BO.java    # 其他业务对象类
│       │   │   │   ├── dto/                  # 数据传输对象目录
│       │   │   │   │   ├── {Request}DTO.java # 请求DTO类
│       │   │   │   │   └── {Other}DTO.java   # 其他DTO类
│       │   │   │   ├── po/                   # 持久化对象目录
│       │   │   │   │   ├── {Entity}PO.java   # 持久化对象类
│       │   │   │   │   └── {Other}PO.java    # 其他持久化对象类
│       │   │   │   └── vo/                   # 视图对象目录
│       │   │   │       ├── {Response}VO.java # 响应VO类
│       │   │   │       └── {Other}VO.java    # 其他VO类
│       │   │   ├── handler/                  # 处理器目录
│       │   │   │   ├── {Event}Handler.java   # 事件处理器
│       │   │   │   └── {Business}Handler.java # 业务处理器
│       │   │   ├── service/                  # 服务接口目录
│       │   │   │   └── {Module}Service.java  # 服务接口类
│       │   │   ├── task/                     # 定时任务目录
│       │   │   │   └── {Schedule}Task.java   # 定时任务类
│       │   │   └── utils/                    # 工具类目录（可选）
│       │   │       └── {Plugin}Utils.java    # 插件专用工具类
│       │   └── resources/                    # 资源文件目录
│       │       ├── config/                   # 配置文件目录
│       │       │   └── plugin.properties     # 插件配置文件
│       │       └── META-INF/                 # META-INF目录
│       │           └── spring.factories      # Spring自动配置文件
│       └── test/                             # 测试目录
│           └── java/com/cet/eem/fusion/{plugin-name}/core/
│               └── service/                  # 服务测试
│                   └── {Module}ServiceTest.java
└── eem-solution-{plugin-name}-service/        # 插件启动类工程
    ├── pom.xml                               # 插件启动类POM文件
    └── src/
        └── main/
            ├── java/com/cet/eem/fusion/config/server/
            │   └── {PluginName}ServiceApplication.java      # 插件启动类
            └── resources/                    # 服务模块配置文件（拷贝demo项目的配置文件即可）
                ├── application-.yml          # 插件配置文件
                ├── application-66.yml          # 开发环境的配置
                ├── application-baseconfig.yml       # 插件基础配置
                └── application-redis.yml          # redis配置
```

### 1.1 目录说明

#### 核心模块 (core) 目录说明：
- **{PluginName}ConfigAutoConfiguration.java**: 插件自动配置类，负责组件扫描和自动装配
- **common/**: 存放公共类，包括常量、枚举、通用工具类等
- **config/**: 存放所有配置类，包括插件路由配置、Bean名称生成器、Swagger配置等
- **consumer/**: 存放消息队列消费者类，处理MQ消息
- **controller/**: 存放REST控制器，处理HTTP请求
- **def/**: 存放常量定义类，定义插件相关的常量
- **entity/bo/**: 存放业务对象，用于业务逻辑处理
- **entity/dto/**: 存放数据传输对象，用于接口间数据传递
- **entity/po/**: 存放持久化对象，对应数据库表结构
- **entity/vo/**: 存放视图对象，用于返回给前端的数据结构
- **handler/**: 存放各种处理器，如事件处理器、业务处理器等
- **service/**: 存放业务服务接口定义
- **task/**: 存放定时任务类，处理定时执行的业务逻辑
- **utils/**: 存放插件专用的工具类（可选）
- **resources/config/**: 存放插件配置文件
- **resources/META-INF/**: 存放Spring自动配置等元信息文件
  - **spring.factories**: Spring自动装配配置文件，只需添加自动装配配置Bean
- **test/**: 存放单元测试和集成测试
- **dao/**: 存放数据访问层接口，定义数据库操作方法
- **impl/**: 存放业务服务接口的具体实现类

#### 启动模块 (service) 目录说明：
- **resources/**: 存放服务模块专用的配置文件，你可以直接拷贝demo项目的同文件夹下的yml配置文件
- 启动类：{PluginName}ServiceApplication
```java
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;

/**
 * <AUTHOR> ({date})
 */
@EnableEurekaClient
@SpringBootApplication
public class {PluginName}ServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run({PluginName}ServiceApplication.class, args);
    }
}

```

### 1.2 文件命名规范

#### Java类文件命名：
- 自动配置类：`{PluginName}ConfigAutoConfiguration.java`
- 控制器：`{Module}Controller.java`
- 服务接口：`{Module}Service.java`
- 服务实现：`{Module}ServiceImpl.java`
- DAO接口：`{Module}Dao.java`
- BO类：`{Business}BO.java`
- DTO类：`{Purpose}DTO.java`
- PO类：`{Entity}PO.java`
- VO类：`{Purpose}VO.java`
- 实体类：`{Entity}.java`
- 消费者类：`{Module}Consumer.java`
- 处理器类：`{Event}Handler.java`
- 定时任务类：`{Schedule}Task.java`
- 常量定义类：`{Plugin}ConstantDef.java`
- 工具类：`{Plugin}{Purpose}Utils.java`
- 配置类：`{Plugin}Configuration.java`

#### 配置文件命名：
- 主配置：`application.yml`

#### 测试文件命名：
- 服务实现测试：`{Module}ServiceImplTest.java`
- 处理器测试：`{Event}HandlerTest.java`

### 2. 常量定义规范

在 `PluginInfoDef.java` 中定义插件常量：

```java
/**
 * {插件中文名称}插件相关常量定义
 */
public static class {PluginName} {
    /**
     * {插件中文名称}插件名称前缀
     */
    public static final String PLUGIN_NAME_PREFIX = "eem-solution-{plugin-name}";
    /**
     * {插件中文名称}模块接口路径前缀
     */
    public static final String INTERFACE_PREFIX = "/eem/solution/{plugin-name}";
}
```

### 3. 插件配置规范

#### 3.1 plugin.properties 配置
注意：plugin.name不能包含`-`字符
```properties
plugin.productname=eem
plugin.version=4.0.0-SNAPSHOT
plugin.name=eemsolution{pluginname}
plugin.runningtype=jar
```

#### 3.2 {PluginName}ConfigAutoConfiguration.java 配置

自动配置类是插件的核心配置入口，负责组件扫描和Bean的自动装配，注意包名不能包含-字符：

```java
/**
 * 描述：自动配置
 * <AUTHOR> ({date})
 */
@Slf4j
@Configuration
@EnableFeignClients(value = "com.cet.eem.fusion.common.feign.feign")
@ComponentScan(value = {"com.cet.eem.fusion.{plugin-name}"},
        nameGenerator = EemFusion{PluginName}BeanNameGenerator.class)
public class {PluginName}ConfigAutoConfiguration {
    public {PluginName}ConfigAutoConfiguration(){
        log.info("Load eem solution {插件中文名称}.");
    }
}
```

**注意事项：**
- 类名必须以`ConfigAutoConfiguration`结尾
- 使用`@ComponentScan`指定扫描包路径
- 指定自定义的Bean名称生成器
- 如需要Feign客户端，添加`@EnableFeignClients`注解
- 构造函数中添加加载日志

#### 3.3 PluginConfiguration.java 配置
```java
/**
 * <AUTHOR> ({date})
 * @description: {插件中文名称}的路由
 */
@Configuration
public class PluginConfiguration extends JarPluginRegister {
    @Override
    public PluginRuntimeInfo getPluginRuntimeInfo() {
        PluginRuntimeInfo pluginRuntimeInfo = new PluginRuntimeInfo();
        //设置插件的url前缀,用于路由,url前缀需要与插件唯一标识保持一致
        pluginRuntimeInfo.setPluginUrlPrefex(PluginInfoDef.{PluginName}.PLUGIN_NAME_PREFIX + "/**");
        //插件唯一标识
        pluginRuntimeInfo.setPluginname(PluginInfoDef.{PluginName}.PLUGIN_NAME_PREFIX);
        //插件的产品线
        pluginRuntimeInfo.setProductname(PluginInfoDef.PRODUCT_NAME);
        return pluginRuntimeInfo;
    }
}
```

#### 3.4 spring.factories 配置

`META-INF/spring.factories` 文件用于Spring Boot自动装配，**必须**配置`ConfigAutoConfiguration`类：

```properties
org.springframework.boot.autoconfigure.EnableAutoConfiguration=\
com.cet.eem.fusion.{plugin-name}.core.{PluginName}ConfigAutoConfiguration
```

**注意事项：**
- **必须**添加`ConfigAutoConfiguration`类，这是插件自动装配的入口
- 只添加需要自动装配的配置类
- 多个配置类用逗号和反斜杠分隔
- 不要添加普通的业务Bean或Service类
- `ConfigAutoConfiguration`与`spring.factories`强相关，缺一不可

### 4. Bean名称生成器规范

```java
/**
 * bean name 生成器
 * <AUTHOR> ({date})
 */
public class EemFusion{PluginName}BeanNameGenerator implements BeanNameGenerator {
    @Override
    public String generateBeanName(BeanDefinition definition, BeanDefinitionRegistry registry) {
        AnnotationBeanNameGenerator annotationBeanNameGenerator = new AnnotationBeanNameGenerator();
        return PluginInfoDef.{PluginName}.PLUGIN_NAME_PREFIX + annotationBeanNameGenerator.generateBeanName(definition, registry);
    }
}
```

### 5. Swagger配置规范

```java
@Configuration
@EnableSwagger2
public class EemSolution{PluginName}SwaggerConfig {
    @Bean
    public Docket docket() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(getApiInfo())
                .groupName("eem solution {插件中文名称}插件")
                .enable(true)
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.cet.eem.fusion.{plugin-name}.core.controller"))
                .paths(PathSelectors.any())
                .build()
                .globalOperationParameters(defaultHeader());
    }

    private ApiInfo getApiInfo() {
        return new ApiInfoBuilder()
                .title("eem solution {插件中文名称}插件接口文档")
                .description("eem solution {插件中文名称}插件接口")
                .version("1.0")
                .build();
    }
}
```

### 6. 控制器规范

```java
@Validated
@RestController
@Api(value = PluginInfoDef.{PluginName}.INTERFACE_PREFIX + "/v1/{module}", tags = "{功能模块}接口")
@RequestMapping(value = PluginInfoDef.{PluginName}.PLUGIN_NAME_PREFIX + "/v1/{module}")
public class {Module}Controller {

    @Resource
    private {Module}Service {module}Service;

    @ApiOperation("{接口功能描述}")
    @PostMapping("/{methodName}")
    public ApiResult<{Response}VO> {methodName}(@RequestBody {Request}DTO {request}DTO) {
        return Result.ok({module}Service.{methodName}({request}DTO));
    }
}
```

### 7. POM文件规范

#### 7.1 主模块 pom.xml
```xml
<artifactId>eem-solution-{plugin-name}</artifactId>
<version>4.0.0-SNAPSHOT</version>
<description>{插件中文名称}插件</description>
<packaging>pom</packaging>

<modules>
    <module>eem-solution-{plugin-name}-core</module>
    <module>eem-solution-{plugin-name}-service</module>
</modules>
```

#### 7.2 子模块 pom.xml
```xml
<artifactId>eem-solution-{plugin-name}-core</artifactId>
<description>{插件中文名称}插件核心模块</description>
```

### 8. 命名规范

- **插件英文名称**：使用小写字母和连字符，如 `demo`、`group-energy`
- **插件中文名称**：简洁明了，如 "demo插件"、"班组能耗插件"
- **接口前缀**：遵循 `/eem/solution/{plugin-name}` 格式
- **包名**：`com.cet.eem.fusion.{plugin-name}`,请注意，不允许出现-字符，只能够由英文字母组成
- **类名**：使用驼峰命名，如 `DemoController`、`EemFusionDemoBeanNameGenerator`

### 9. 插件注册清单

新插件开发完成后，需要在 `README.md` 中更新插件清单，如果已经存在，则跳过这部分编写

| 插件解释 | 插件名 | 接口前缀 |
|---------|--------|----------|
| {插件中文名称} | eem-solution-{plugin-name} | /eem/solution/{plugin-name} |

### 10. 迁移检查清单

完成插件开发或迁移后，需要做检查复盘，补全遗漏和不对的地方，请检查以下项目：

- [ ] 插件常量定义已添加到 `PluginInfoDef.java`
- [ ] 检查插件的文件夹路径，命名不要带-，录入如果时group-energy，则修改为groupenergy，请注意同步修改其他类的引用
- [ ] `plugin.properties` 配置正确
- [ ] `{PluginName}ConfigAutoConfiguration.java` 自动配置类创建正确
- [ ] `spring.factories` 配置了`ConfigAutoConfiguration`类
- [ ] `PluginConfiguration.java` 路由配置正确
- [ ] Bean名称生成器类名和实现正确
- [ ] Swagger配置中的包路径和描述正确
- [ ] 控制器使用正确的接口前缀和插件名称前缀
- [ ] POM文件中的模块名称和描述正确
- [ ] 包名结构符合规范
- [ ] `{PluginName}ConfigAutoConfiguration`与`spring.factories`配置一致
- [ ] Controller目录位于core目录下，而不是service模块下
- [ ] 是否Contoller目录下只有Controller，如果有task相关请独立出来，放到task目录
- [ ] 插件的service项目只有启动类和配置文件，其他都在core项目中
- [ ] 原项目中所有的内容已经迁移完毕，没有代码遗漏和缺失