# Maintenance Plugin File Migration Script
param(
    [string]$SourcePath = "energy-base\cet-eem-maintenance\cet-eem-bll-maintenance\src\main\java\com\cet\eem\bll\maintenance",
    [string]$TargetPath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java\com\cet\eem\fusion\maintenance\core"
)

Write-Host "Starting maintenance plugin file migration..." -ForegroundColor Green
Write-Host "Source path: $SourcePath" -ForegroundColor Yellow
Write-Host "Target path: $TargetPath" -ForegroundColor Yellow

# Get all Java files
$javaFiles = Get-ChildItem -Path $SourcePath -Filter "*.java" -Recurse

Write-Host "Found $($javaFiles.Count) Java files to migrate" -ForegroundColor Cyan

$migratedCount = 0
$errorCount = 0

foreach ($file in $javaFiles) {
    try {
        # Calculate relative path
        $relativePath = $file.FullName.Substring((Resolve-Path $SourcePath).Path.Length + 1)
        $targetFile = Join-Path $TargetPath $relativePath

        # Create target directory
        $targetDir = Split-Path $targetFile -Parent
        if (!(Test-Path $targetDir)) {
            New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
        }

        # Read source file content
        $content = Get-Content $file.FullName -Raw -Encoding UTF8

        # Update package declaration
        $content = $content -replace "package com\.cet\.eem\.bll\.maintenance", "package com.cet.eem.fusion.maintenance.core"

        # Update import statements
        $content = $content -replace "import com\.cet\.eem\.bll\.maintenance\.", "import com.cet.eem.fusion.maintenance.core."

        # Write to target file
        Set-Content -Path $targetFile -Value $content -Encoding UTF8

        $migratedCount++
        Write-Host "✓ Migrated: $relativePath" -ForegroundColor Green
        
    } catch {
        $errorCount++
        Write-Host "✗ Error: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nMigration completed!" -ForegroundColor Green
Write-Host "Successfully migrated: $migratedCount files" -ForegroundColor Cyan
Write-Host "Errors: $errorCount files" -ForegroundColor Red
