﻿package com.cet.eem.fusion.maintenance.core.controller;

import com.cet.eem.fusion.maintenance.core.controller.bff.SignInBffController;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName : SignInController
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-03-12 16:49
 */
@Api(value = "/eem/v1/signin", tags = "签到点管理")
@RequestMapping(value = "/eem/solution/maintenance/eem/v1/signin")
@RestController
@Validated
public class SignInController extends SignInBffController {

}


