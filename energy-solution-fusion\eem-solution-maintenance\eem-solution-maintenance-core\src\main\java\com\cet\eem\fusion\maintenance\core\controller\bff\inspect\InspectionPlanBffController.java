﻿package com.cet.eem.fusion.maintenance.core.controller.bff.inspect;

import com.cet.eem.bll.common.log.annotation.OperationLog;
import com.cet.eem.bll.common.log.constant.EEMOperationLogType;
import com.cet.eem.bll.common.log.constant.EnumOperationSubType;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;
import com.cet.eem.fusion.maintenance.core.model.plan.AddInspectionPlanRequest;
import com.cet.eem.fusion.maintenance.core.model.plan.EditInspectionPlanRequest;
import com.cet.eem.fusion.maintenance.core.model.plan.InspectionPlanSheetVo;
import com.cet.eem.fusion.maintenance.core.model.plan.QueryInspectionPlanRequest;
import com.cet.eem.fusion.maintenance.core.schedule.domain.PlanSheetDomain;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectionPlanService;
import com.cet.electric.commons.ApiResult;

import com.cet.electric.commons.ApiResult;
import io.swagger.annotations.ApiOperation;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName : InspectionPlanBffController
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-21 14:19
 */

public class InspectionPlanBffController {

    @Autowired
    private InspectionPlanService inspectionPlanService;

    @Autowired
    private PlanSheetDomain planSheetDomain;

    @ApiOperation(value = "新增巡检计划")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR_PLAN, subType = EnumOperationSubType.ADD, description = "【新增巡检计划】")
    @PostMapping
    public ApiResult<PlanSheet> addInspectionPlan(@Valid @RequestBody AddInspectionPlanRequest addInspectionPlanRequest) throws SchedulerException {
        return ApiResult.ok(inspectionPlanService.addInspectionPlan(addInspectionPlanRequest));
    }

    @ApiOperation(value = "编辑巡检计划")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR_PLAN, subType = EnumOperationSubType.UPDATE, description = "【更新巡检计划】")
    @PatchMapping
    public ApiResult<PlanSheet> editInspectionPlan(@Valid @RequestBody EditInspectionPlanRequest editInspectionPlanRequest) throws SchedulerException {
        return ApiResult.ok(inspectionPlanService.editInspectionPlan(editInspectionPlanRequest));
    }

    @ApiOperation(value = "查询当前项目巡检计划")
    @PostMapping("/query")
    public ApiResult<List<InspectionPlanSheetVo>> queryInspectionPlan(@Valid @RequestBody QueryInspectionPlanRequest queryInspectionPlanRequest) {
        return inspectionPlanService.queryInspectionPlan(queryInspectionPlanRequest);
    }

    @ApiOperation(value = "删除计划")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR_PLAN, subType = EnumOperationSubType.DELETE, description = "【删除计划】")
    @DeleteMapping
    public ApiResult<Void> deleteInspectionPlan(@RequestBody List<Long> inspectionPlanIds) throws SchedulerException {
        inspectionPlanService.deletePlan(inspectionPlanIds);
        return ApiResult.ok();
    }

    @ApiOperation(value = "启用巡检计划")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR_PLAN, subType = EnumOperationSubType.UPDATE, description = "【启用巡检计划】")
    @PostMapping("/enable")
    public ApiResult<Void> enablePlanSheet(@RequestBody List<Long> ids) throws SchedulerException {
        inspectionPlanService.enablePlanSheet(ids);
        return ApiResult.ok();
    }

    @ApiOperation(value = "禁用巡检计划")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR_PLAN, subType = EnumOperationSubType.UPDATE, description = "【禁用巡检计划】")
    @PostMapping("disable")
    public ApiResult<Void> updatePlanSheetBatch(@RequestBody List<Long> ids) throws SchedulerException {
        inspectionPlanService.disablePlanSheet(ids);
        return ApiResult.ok();
    }

    @ApiOperation(value = "根据巡检计划id查询trigger")
    @GetMapping("trigger/{planSheetId}")
    public ApiResult<List<Trigger>> updatePlanSheetBatch(@PathVariable("planSheetId") Long planSheetId) throws SchedulerException {
        List<Trigger> result = (List<Trigger>) planSheetDomain.queryTriggerByJob(planSheetId);
        return ApiResult.ok(result);
    }

    @ApiOperation(value = "根据巡检计划id查询执行计划")
    @GetMapping("executePlan/{planSheetId}/{size}")
    public ApiResult<List<Long>> updatePlanSheetBatch(@PathVariable("planSheetId") Long planSheetId,
                                                   @PathVariable("size") int size) throws SchedulerException {
        List<Long> result = planSheetDomain.queryExecutePlan(planSheetId, size);
        return ApiResult.ok(result);
    }

    @ApiOperation(value = "根据巡检计划id更新巡检计划状态：是否被引用 ")
    @PostMapping("Used/{planSheetId}")
    public ApiResult<PlanSheet> updatePlanSheetUsed(@PathVariable("planSheetId") Long planSheetId) throws SchedulerException {
        return ApiResult.ok(inspectionPlanService.editUsed(planSheetId));
    }
}


