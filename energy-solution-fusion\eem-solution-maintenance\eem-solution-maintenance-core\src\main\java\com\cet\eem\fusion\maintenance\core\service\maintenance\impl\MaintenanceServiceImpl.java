﻿package com.cet.eem.fusion.maintenance.core.service.maintenance.impl;

import com.alibaba.excel.EasyExcel;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.*;
import com.cet.eem.bll.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.MaintenanceGroupWithSubLayer;
import com.cet.eem.bll.common.util.ExcelValidationUtils;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.dao.PlanSheetDao;
import com.cet.eem.fusion.maintenance.core.dao.devicecomponent.DeviceDao;
import com.cet.eem.fusion.maintenance.core.dao.devicecomponent.SparePartsDao;
import com.cet.eem.fusion.maintenance.core.dao.maintenance.MaintenanceGroupDao;
import com.cet.eem.fusion.maintenance.core.dao.maintenance.MaintenanceItemDao;
import com.cet.eem.fusion.maintenance.core.dao.maintenance.MaintenanceTypeDefineDao;
import com.cet.eem.fusion.maintenance.core.listener.MaintenanceItemImport;
import com.cet.eem.fusion.maintenance.core.listener.MaintenanceItemListener;
import com.cet.eem.fusion.maintenance.core.model.maintance.item.*;
import com.cet.eem.fusion.maintenance.core.service.maintenance.MaintenanceServcie;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.utils.ParamUtils;
import com.cet.eem.common.constant.ExcelType;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.def.label.NodeLabelDef;
import com.cet.eem.common.exception.ValidationException;
import com.cet.eem.fusion.common.utils.file.FileUtils;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.common.utils.PoiExcelUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryResultContentTaker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import com.cet.eem.fusion.common.def.common.ContentTypeDef;

/**
 * @ClassName : MaintenanceServiceImpl
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-10 14:00
 */
@Slf4j
@Service
public class MaintenanceServiceImpl implements MaintenanceServcie {

    @Autowired
    private MaintenanceGroupDao maintenanceGroupDao;

    @Autowired
    private MaintenanceItemDao maintenanceItemDao;

    @Autowired
    private ModelServiceUtils modelServiceUtils;

    @Autowired
    private SparePartsDao sparePartsDao;

    @Autowired
    private PlanSheetDao planSheetDao;

    @Autowired
    private DeviceDao deviceDao;

    @Autowired
    private MaintenanceTypeDefineDao maintenanceTypeDefineDao;

    /**
     * 无型号的模型
     */
    private static final List<String> NO_MODEL_LABEL = Arrays.asList(NodeLabelDef.ROOM, NodeLabelDef.MANU_EQUIPMENT);

    @Override
    public List<MaintenanceGroup> queryAllGroupInThisProject() {
        LambdaQueryWrapper<MaintenanceGroup> queryWrapper = LambdaQueryWrapper.of(MaintenanceGroup.class)
                .orderByAsc(BaseEntity::getId);
        Long projectId = GlobalInfoUtils.getTenantId();
        if (Objects.nonNull(projectId)) {
            queryWrapper.eq(MaintenanceGroup::getProjectId, projectId);
        }
        return maintenanceGroupDao.selectList(queryWrapper);
    }

    @Override
    public MaintenanceGroup addMaintenanceGroup(AddMaintenanceGroupRequest addMaintenanceGroupRequest) {
        checkSameNameGroupWhileCreate(addMaintenanceGroupRequest.getName());
        MaintenanceGroup maintenanceGroup = new MaintenanceGroup();
        maintenanceGroup.setName(addMaintenanceGroupRequest.getName());
        maintenanceGroup.setProjectId(GlobalInfoUtils.getTenantId());
        maintenanceGroupDao.insert(maintenanceGroup);
        return maintenanceGroup;
    }

    @Override
    public MaintenanceGroup editMaintenanceGroup(EditMaintenanceGroupRequest editMaintenanceGroupRequest) {
        checkSameNameGroupWhileEdit(editMaintenanceGroupRequest.getId(), editMaintenanceGroupRequest.getName());
        MaintenanceGroup maintenanceGroup = maintenanceGroupDao.selectById(editMaintenanceGroupRequest.getId());
        Assert.notNull(maintenanceGroup, "记录不存在！");
        maintenanceGroup.setId(editMaintenanceGroupRequest.getId());
        maintenanceGroup.setName(editMaintenanceGroupRequest.getName());
        maintenanceGroup.setProjectId(GlobalInfoUtils.getTenantId());
        maintenanceGroupDao.updateById(maintenanceGroup);
        return maintenanceGroup;
    }


    @Override
    public void deleteMaintenanceGroup(Collection<Long> ids) {
        LambdaQueryWrapper<MaintenanceGroup> queryWrapper = LambdaQueryWrapper.of(MaintenanceGroup.class);
        queryWrapper.in(MaintenanceGroup::getId, ids);
        List<MaintenanceGroupWithSubLayer> maintenanceGroupWithSubLayers = maintenanceGroupDao.selectRelatedList(MaintenanceGroupWithSubLayer.class, queryWrapper);
        if (CollectionUtils.isEmpty(maintenanceGroupWithSubLayers)) {
            return;
        }
        for (MaintenanceGroupWithSubLayer maintenanceGroupWithSubLayer : maintenanceGroupWithSubLayers) {
            List<MaintenanceItem> maintenanceItemList = maintenanceGroupWithSubLayer.getMaintenanceItemList();
            Assert.isTrue(CollectionUtils.isEmpty(maintenanceItemList), "分组下存在维保项,无法删除");
        }
        List<Long> groupIds = maintenanceGroupWithSubLayers.stream().map(MaintenanceGroupWithSubLayer::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(groupIds)) {
            maintenanceGroupDao.deleteBatchIds(groupIds);
        }
    }

    @Override
    public List<MaintenanceItemVo> queryAllMaintenanceItem(Long maintenanceGroupId) {
        List<MaintenanceItem> maintenanceItemList = getMaintenanceGroupWithSubLayer(maintenanceGroupId);
        List<MaintenanceItemVo> maintenanceItemVos = transferMaintenanceItemToVo(maintenanceItemList);
        return maintenanceItemVos.stream().sorted(Comparator.comparing(MaintenanceItemVo::getSort)).collect(Collectors.toList());
    }

    private List<MaintenanceItem> getMaintenanceGroupWithSubLayer(Long maintenanceGroupId) {
        LambdaQueryWrapper<MaintenanceGroup> queryWrapper = LambdaQueryWrapper.of(MaintenanceGroup.class);
        queryWrapper.eq(MaintenanceGroup::getId, maintenanceGroupId);
        List<MaintenanceGroupWithSubLayer> maintenanceGroupWithSubLayers = maintenanceGroupDao.selectRelatedList(MaintenanceGroupWithSubLayer.class, queryWrapper);
        MaintenanceGroupWithSubLayer maintenanceGroupWithSubLayer = maintenanceGroupWithSubLayers.stream().findAny().orElse(null);
        if (Objects.isNull(maintenanceGroupWithSubLayer)) {
            return Collections.emptyList();
        }

        return maintenanceGroupWithSubLayer.getMaintenanceItemList();
    }

    @Override
    public List<MaintenanceItemVo> queryMaintenanceItemByPlanSheetId(Long planSheetId) {
        PlanSheet planSheet = planSheetDao.selectById(planSheetId);
        if (Objects.isNull(planSheet)) {
            return Collections.emptyList();
        }
        if (WorkSheetTaskType.MAINTENANCE != planSheet.getWorkSheetType()) {
            return Collections.emptyList();
        }
        if (Objects.isNull(planSheet.getMaintenanceExtend()) || CollectionUtils.isEmpty(planSheet.getMaintenanceExtend().getMaintenanceItems())) {
            return Collections.emptyList();
        }

        Set<Long> ids = planSheet.getMaintenanceExtend().getMaintenanceItems().stream().map(MaintenanceExtend.GroupItemMapping::getItemIds).flatMap(Collection::stream).collect(Collectors.toSet());
        List<MaintenanceItemVo> maintenanceItemVos = transferMaintenanceItemToVo(maintenanceItemDao.selectBatchIds(ids));
        addGroup(maintenanceItemVos);
        maintenanceItemVos.sort((v1, v2) -> {
            int sort = CommonUtils.sort(v1.getGroupId(), v2.getGroupId(), true);
            if (sort != 0) {
                return sort;
            }

            return CommonUtils.sort(v1.getSort(), v2.getSort(), true);
        });
        return maintenanceItemVos;
    }

    @Override
    public List<MaintenanceItemVo> queryMaintenanceItemByPlanSheetId(MaintenanceItemSearchVo searchVo) {
        List<MaintenanceItemVo> maintenanceItems = queryAllMaintenanceItem(searchVo.getMaintenanceGroupId());
        if (NO_MODEL_LABEL.contains(searchVo.getNodeLabel())) {
            return maintenanceItems.stream().filter(it -> !ParamUtils.checkPrimaryKeyValid(it.getSparePartId())).collect(Collectors.toList());
        }

        List<SpareParts> spareParts = deviceDao.querySparepartsStorageByDevice(searchVo.getModel(), searchVo.getNodeLabel());
        Set<Long> sparePartIds = spareParts.stream().map(BaseEntity::getId).collect(Collectors.toSet());
        return maintenanceItems.stream().filter(it -> !ParamUtils.checkPrimaryKeyValid(it.getSparePartId()) || sparePartIds.contains(it.getSparePartId())).collect(Collectors.toList());
    }

    @Override
    public MaintenanceItem addMaintenanceItem(AddMaintenanceItemRequest addMaintenanceItemRequest) {
        List<MaintenanceItem> maintenanceGroupWithSubLayer = getMaintenanceGroupWithSubLayer(addMaintenanceItemRequest.getMaintenanceGroupId());
        int maxSort = getMaxSort(maintenanceGroupWithSubLayer);

        MaintenanceItem maintenanceItem = new MaintenanceItem();
        maintenanceItem.setContent(addMaintenanceItemRequest.getContent());
        maintenanceItem.setMaintenanceType(addMaintenanceItemRequest.getMaintenanceType());
        maintenanceItem.setSparePartId(addMaintenanceItemRequest.getSparePartId());
        maintenanceItem.setNumber(addMaintenanceItemRequest.getNumber());
        maintenanceItem.setSort(++maxSort);
        List<Map<String, Object>> maps = maintenanceGroupDao.insertChild(addMaintenanceItemRequest.getMaintenanceGroupId(), Collections.singletonList(maintenanceItem));
        if (CollectionUtils.isNotEmpty(maps)) {
            Map<String, Object> map = maps.stream().findFirst().orElse(null);
            maintenanceItem.setId(QueryResultContentTaker.getId(map));
        }
        return maintenanceItem;
    }

    /**
     * 获取最大排序值
     *
     * @param maintenanceGroupWithSubLayer
     * @return
     */
    private int getMaxSort(List<MaintenanceItem> maintenanceGroupWithSubLayer) {
        int maxSort = 0;
        if (CollectionUtils.isNotEmpty(maintenanceGroupWithSubLayer)) {
            OptionalInt max = maintenanceGroupWithSubLayer.stream().map(MaintenanceItem::getSort).filter(Objects::nonNull).mapToInt(it -> it).max();
            if (max.isPresent()) {
                maxSort = max.getAsInt();
            }
        }
        return maxSort;
    }

    @Override
    public MaintenanceItem editMaintenanceItem(EditMaintenanceItemRequest editMaintenanceItemRequest) {
        MaintenanceItem maintenanceItem = handlerModelChange(editMaintenanceItemRequest);
        handleGroupChange(editMaintenanceItemRequest, maintenanceItem);
        return maintenanceItem;
    }

    @Override
    public void editMaintenanceItemSort(List<MaintenanceItemSortVo> maintenanceItemSortVos) {
        if (CollectionUtils.isEmpty(maintenanceItemSortVos)) {
            return;
        }

        List<String> writeFields = Collections.singletonList(ColumnDef.SORT);
        List<List<Object>> writeDataList = new ArrayList<>();
        List<List<Object>> filterDataList = new ArrayList<>();
        for (MaintenanceItemSortVo item : maintenanceItemSortVos) {
            writeDataList.add(Collections.singletonList(item.getSort()));
            filterDataList.add(Collections.singletonList(item.getId()));
        }
        modelServiceUtils.writeDataBatch(ModelLabelDef.MAINTENANCE_ITEM, false, writeFields, writeDataList, filterDataList);
    }

    private void handleGroupChange(EditMaintenanceItemRequest editMaintenanceItemRequest, MaintenanceItem maintenanceItem) {
        List<MaintenanceGroup> maintenanceGroups = maintenanceGroupDao.queryItemGroup(editMaintenanceItemRequest.getId());
        List<Long> ids = maintenanceGroups.stream().map(MaintenanceGroup::getId).collect(Collectors.toList());
        if (ids.contains(editMaintenanceItemRequest.getMaintenanceGroupId())) {
            return;
        }
        for (MaintenanceGroup group : maintenanceGroups) {
            maintenanceGroupDao.moveChild(group.getId(), Collections.singletonList(maintenanceItem));
        }
        maintenanceGroupDao.insertChild(editMaintenanceItemRequest.getMaintenanceGroupId(), Collections.singletonList(maintenanceItem));
    }

    private MaintenanceItem handlerModelChange(EditMaintenanceItemRequest editMaintenanceItemRequest) {
        MaintenanceItem maintenanceItem = maintenanceItemDao.selectById(editMaintenanceItemRequest.getId());
        Assert.notNull(maintenanceItem, "记录不存在！");
        maintenanceItem.setId(editMaintenanceItemRequest.getId());
        maintenanceItem.setContent(editMaintenanceItemRequest.getContent());
        maintenanceItem.setMaintenanceType(editMaintenanceItemRequest.getMaintenanceType());
        maintenanceItem.setSparePartId(editMaintenanceItemRequest.getSparePartId());
        maintenanceItem.setNumber(editMaintenanceItemRequest.getNumber());
        maintenanceItemDao.updateById(maintenanceItem);
        return maintenanceItem;
    }

    @Override
    public void deleteMaintenanceItem(List<Long> ids) {
        List<Map<String, Object>> maintenanceItems = maintenanceItemDao.queryMaintenanceWithPlanSheet(ids);
        for (Map<String, Object> item : maintenanceItems) {
            List<Map<String, Object>> children = QueryResultContentTaker.getChildren(item);
            if (CollectionUtils.isNotEmpty(children)) {
                throw new ValidationException(String.format("维保内容为[%s]的维保项已经被使用，不允许删除！", item.get(ColumnDef.CONTENT)));
            }
        }
        maintenanceItemDao.deleteBatchIds(ids);
    }

    @Override
    public void exportItem(HttpServletResponse response, List<Long> ids) {
        LambdaQueryWrapper<MaintenanceGroup> queryWrapper = LambdaQueryWrapper.of(MaintenanceGroup.class);
        queryWrapper.in(MaintenanceGroup::getId, ids);
        //查询维保分组下的维保项
        List<MaintenanceGroupWithSubLayer> maintenanceGroupWithSubLayers = maintenanceGroupDao.selectRelatedList(MaintenanceGroupWithSubLayer.class, queryWrapper);
        String fileName = "维保项目导出" + LocalDateTime.now().format(TimeUtil.SECONDTIMEFORMAT);

        List<Integer> colWidth = Arrays.asList(18, 18, 18, 18, 18, 18);
        //处理数据，添加备件的单位、名称等信息，同时处理成维保分组信息+维保项目信息组合的list
        List<MaintenanceItemVo> maintenanceItemVos = handleLayer(maintenanceGroupWithSubLayers);

        try (Workbook workBook = PoiExcelUtils.createWorkBook(ExcelType.BIG_DATA)) {
            PoiExcelUtils.createSheet(workBook, fileName, (sheet, baseCellStyle, rowIndex) -> {
                int rowNum = 0;
                // 写入导出连接关系表数据
                writeHeader(workBook, sheet, baseCellStyle, rowNum++);
                //写入数据
                writeRecord(sheet, baseCellStyle, rowNum, maintenanceItemVos);
            }, colWidth);
            FileUtils.downloadExcel(response, workBook, fileName, ContentTypeDef.APPLICATION_MSEXCEL);
        } catch (Exception e) {
            log.error("维保项目导出异常", e);
        }
    }

    private List<MaintenanceItemVo> handleLayer(List<MaintenanceGroupWithSubLayer> maintenanceGroupWithSubLayers) {
        if (CollectionUtils.isEmpty(maintenanceGroupWithSubLayers)) {
            return Collections.emptyList();
        }
        List<MaintenanceItemVo> resultList = transMaintenanceGroupWithLayerToVos(maintenanceGroupWithSubLayers);
        Set<Long> sparePartIds = maintenanceGroupWithSubLayers.stream().filter(maintenanceGroupWithSubLayer -> CollectionUtils.isNotEmpty(maintenanceGroupWithSubLayer.getMaintenanceItemList()))
                .flatMap(maintenanceGroupWithSubLayer -> maintenanceGroupWithSubLayer.getMaintenanceItemList().stream()).
                map(MaintenanceItem::getSparePartId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(sparePartIds)) {
            List<SpareParts> spareParts = sparePartsDao.selectBatchIds(sparePartIds);
            Map<Long, SpareParts> sparePartsMap = spareParts.stream().collect(Collectors.toMap(SpareParts::getId, it -> it));
            resultList.forEach(s -> {
                SpareParts obj = sparePartsMap.get(s.getSparePartId());
                if (Objects.nonNull(obj)) {
                    s.setSparePartName(obj.getName());
                    s.setUnit(obj.getUnit());
                }
            });
        }
        //维保类型赋值
        Map<Long, String> maintenanceTypeEnum = queryMaintenanceTypeEnum();
        resultList.forEach(s -> {
            if (Objects.nonNull(s.getMaintenanceType())) {
                s.setMaintenanceTypeName(maintenanceTypeEnum.get(s.getMaintenanceType()));
            }
        });

        return resultList;
    }


    /**
     * 写入数据
     *
     * @param sheet
     * @param baseCellStyle
     * @param rowNum
     * @param maintenanceItemVos
     */
    private void writeRecord(Sheet sheet, CellStyle baseCellStyle, int rowNum, List<MaintenanceItemVo> maintenanceItemVos) {
        maintenanceItemVos.sort((v1, v2) -> {
            int sort = CommonUtils.sort(v1.getGroupId(), v2.getGroupId(), true);
            if (sort != 0) {
                return sort;
            }

            return CommonUtils.sort(v1.getSort(), v2.getSort(), true);
        });
        int col;
        for (MaintenanceItemVo maintenanceItemVo : maintenanceItemVos) {

            col = 0;
            Row row = PoiExcelUtils.createRow(sheet, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, (maintenanceItemVo.getGroupName()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, (maintenanceItemVo.getMaintenanceTypeName()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, (maintenanceItemVo.getContent()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, CommonUtils.setDeviceNameAndIdAndModelLabel(maintenanceItemVo.getSparePartName(), ModelLabelDef.SPARE_PARTS_STORAGE, maintenanceItemVo.getSparePartId()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, (maintenanceItemVo.getNumber()));
            PoiExcelUtils.createCell(row, col, baseCellStyle, (maintenanceItemVo.getUnit()));
            rowNum++;

        }

    }

    private void writeHeader(Workbook workbook, Sheet sheet, CellStyle baseCellStyle, int startRow) {
        LinkedHashMap<String, CellStyle> headerMap = new LinkedHashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        int col = 0;
        List<String> spareNodeList = new ArrayList<>();
        List<SpareParts> sparePartsList = sparePartsDao.selectAll();
        for (SpareParts spareParts : sparePartsList) {
            spareNodeList.add(CommonUtils.setDeviceNameAndIdAndModelLabel(spareParts.getName(), ModelLabelDef.SPARE_PARTS_STORAGE, spareParts.getId()));
        }
        String[] sparePartsnodeValidation = spareNodeList.toArray(new String[0]);
        CellStyle requiredStyle = createRequiredStyle(workbook, baseCellStyle);
        headerMap.put("维保分组", requiredStyle);
        headerMap.put("维保方式", requiredStyle);
        headerMap.put("维保内容", requiredStyle);
        headerMap.put("零部件类型", baseCellStyle);
        col = 3;
        ExcelValidationUtils.addValidationData(workbook, "备件信息", sheet, sparePartsnodeValidation, startRow + 1, col++);
        headerMap.put("零部件数量", baseCellStyle);
        headerMap.put("零部件单位", baseCellStyle);
        PoiExcelUtils.createHeaderName(sheet, startRow, headerMap);
    }

    private CellStyle createRequiredStyle(Workbook workbook, CellStyle baseCellStyle) {
        CellStyle requiredCellStyle = workbook.createCellStyle();
        requiredCellStyle.cloneStyleFrom(baseCellStyle);
        Font font = PoiExcelUtils.createFont(workbook, true, null, null, HSSFColor.HSSFColorPredefined.RED.getIndex());
        requiredCellStyle.setFont(font);
        return requiredCellStyle;
    }


    @Override
    public void importItem(MultipartFile file,Long projectId) throws IOException {
        List<MaintenanceItemVo> maintenanceItemVos = new ArrayList<>();
        //读取数据
        EasyExcel.read(file.getInputStream(), MaintenanceItemImport.class, new MaintenanceItemListener(maintenanceItemVos)).sheet().doRead();
        if (CollectionUtils.isEmpty(maintenanceItemVos)) {
            return;
        }
        //判断文件里同一个分组下，有没有重复的维保项目
        MaintenanceItemVo repeatInFile = isRepeatInFile(maintenanceItemVos);
        if (null != repeatInFile) {
            Assert.isNull(repeatInFile, "分组名称为" + repeatInFile.getGroupName() + "下的维保方式为" + repeatInFile.getMaintenanceTypeName() + "的描述信息为" + repeatInFile.getContent() + "的维保项目的信息重复");
        }
        writeMaintenanceItem(maintenanceItemVos,projectId);


    }

    private List<MaintenanceItemVo> transMaintenanceGroupWithLayerToVos(List<MaintenanceGroupWithSubLayer> maintenanceGroupWithSubLayers) {
        Map<Long, String> map = queryMaintenanceTypeEnum();
        List<MaintenanceItemVo> resultList = new ArrayList<>();
        //数据格式的转换
        for (MaintenanceGroupWithSubLayer maintenanceGroupWithSubLayer : maintenanceGroupWithSubLayers) {
            //这部分是为了没有维保项目写入时，写入维保分组的信息
            MaintenanceItemVo maintenanceItemVo = new MaintenanceItemVo();
            maintenanceItemVo.setGroupId(maintenanceGroupWithSubLayer.getId());
            maintenanceItemVo.setGroupName(maintenanceGroupWithSubLayer.getName());
            if (CollectionUtils.isEmpty(maintenanceGroupWithSubLayer.getMaintenanceItemList())) {
                resultList.add(maintenanceItemVo);
                continue;
            }
            for (MaintenanceItem maintenanceItem : maintenanceGroupWithSubLayer.getMaintenanceItemList()) {
                MaintenanceItemVo maintenanceItemVo1 = new MaintenanceItemVo();
                maintenanceItemVo1.setGroupId(maintenanceGroupWithSubLayer.getId());
                maintenanceItemVo1.setGroupName(maintenanceGroupWithSubLayer.getName());
                BeanUtils.copyProperties(maintenanceItem, maintenanceItemVo1);
                maintenanceItemVo1.setMaintenanceTypeName(map.get(maintenanceItem.getMaintenanceType()));
                resultList.add(maintenanceItemVo1);
            }

        }
        return resultList;
    }

    /**
     * 比较导入的数据和数据库中的数据，同一组下，用content判重，如果导入的数据存在和数据库中组别和content相同的情况，作为编辑。
     *
     * @param maintenanceItemVos
     * @param maintenanceItemVoList
     * @return
     */
    private List<MaintenanceItemVo> queryUpdateData(List<MaintenanceItemVo> maintenanceItemVos, List<MaintenanceItemVo> maintenanceItemVoList) {
        if (CollectionUtils.isEmpty(maintenanceItemVoList)) {
            return maintenanceItemVos;
        }
        Map<String, Long> stringLongMap = queryMaintenanceTypeEnumByNmae();
        //筛选出组别和维保描述相同的项，这部分是新增的内容。
        List<MaintenanceItemVo> add = new ArrayList<>();
        //这是为了处理数据时，拼接的数据分组信息出现错误得情况。
        Set<BaseVo> collect = maintenanceItemVoList.stream().map(maintenanceItemVo -> new BaseVo(maintenanceItemVo.getGroupId(), ModelLabelDef.MAINTENANCE_GROUP, maintenanceItemVo.getGroupName())).collect(Collectors.toSet());
        Map<String, Long> nodeMap = new HashMap<>();
        for (BaseVo baseVo : collect) {
            nodeMap.put(baseVo.getName(), baseVo.getId());
        }
        //需要编辑的内容
        List<MaintenanceItemVo> updateVos = new ArrayList<>();
        for (MaintenanceItemVo it : maintenanceItemVos) {
            int i = 0;
            for (MaintenanceItemVo maintenanceItemVo : maintenanceItemVoList) {
                if (it.getGroupName().equals(maintenanceItemVo.getGroupName()) && it.getContent().equals(maintenanceItemVo.getContent())
                        && Objects.equals(it.getMaintenanceTypeName(), maintenanceItemVo.getMaintenanceTypeName()) && Objects.equals(it.getSparePartId(), maintenanceItemVo.getSparePartId())) {
                    it.setMaintenanceType(stringLongMap.get(it.getMaintenanceTypeName()));
                    it.setId(maintenanceItemVo.getId());
                    it.setSort(maintenanceItemVo.getSort());
                    updateVos.add(it);
                    break;
                } else {
                    if (i == maintenanceItemVoList.size() - 1) {
                        it.setGroupId(nodeMap.get(it.getGroupName()));
                        add.add(it);
                        break;
                    }
                    i++;
                }
            }
        }
        List<MaintenanceItem> update = new ArrayList<>();
        for (MaintenanceItemVo maintenanceItemVo : updateVos) {
            MaintenanceItem maintenanceItem = new MaintenanceItemVo();
            BeanUtils.copyProperties(maintenanceItemVo, maintenanceItem);
            update.add(maintenanceItem);
        }
        //写入
        modelServiceUtils.writeData(update, MaintenanceItem.class);
        return add;
    }

    private void insertMaintenanceGroup(List<MaintenanceItemVo> maintenanceItemVos,Long projectId) {
        Set<String> collect = maintenanceItemVos.stream().map(MaintenanceItemVo::getGroupName).collect(Collectors.toSet());
        //根据名称查询维保分组
        List<MaintenanceGroupWithSubLayer> maintenanceGroupWithSubLayers = maintenanceGroupDao.queryMaintenanceGroupWithName(new ArrayList<>(collect),projectId);
        Set<String> collectInDataBase = maintenanceGroupWithSubLayers.stream().map(MaintenanceGroupWithSubLayer::getName).collect(Collectors.toSet());
        //筛选新增的维保分组名称
        Set<String> collectResult = collect.stream().filter(s -> !collectInDataBase.contains(s)).collect(Collectors.toSet());
        List<MaintenanceGroup> maintenanceGroups = new ArrayList<>();
        for (String name : collectResult) {
            MaintenanceGroup maintenanceGroup = new MaintenanceGroup();
            maintenanceGroup.setName(name);
            maintenanceGroup.setProjectId(projectId);
            maintenanceGroups.add(maintenanceGroup);
        }
        //写入数据
        modelServiceUtils.writeData(maintenanceGroups, MaintenanceGroup.class);

    }

    private void insertMaintenanceTypeDefine(List<MaintenanceItemVo> maintenanceItemVos,Long projectId) {
        List<String> collect = maintenanceItemVos.stream().map(MaintenanceItemVo::getMaintenanceTypeName).collect(Collectors.toList());
        List<MaintenanceTypeDefine> maintenanceTypeDefines = maintenanceTypeDefineDao.queryTypeByNames(collect, projectId);
        Set<String> collectType = maintenanceTypeDefines.stream().map(MaintenanceTypeDefine::getName).collect(Collectors.toSet());
        //筛选新增的维保分类名称
        Set<String> collectResult = collect.stream().filter(s -> !collectType.contains(s)).collect(Collectors.toSet());
        List<MaintenanceTypeDefine> maintenanceTypeDefineList = new ArrayList<>();
        for (String name : collectResult) {
            MaintenanceTypeDefine maintenanceTypeDefine = new MaintenanceTypeDefine();
            maintenanceTypeDefine.setProjectId(projectId);
            maintenanceTypeDefine.setName(name);
            maintenanceTypeDefineList.add(maintenanceTypeDefine);
        }
        //写入数据
        modelServiceUtils.writeData(maintenanceTypeDefineList, MaintenanceTypeDefine.class);
    }

    private void writeMaintenanceItem(List<MaintenanceItemVo> maintenanceItemVos,Long projectId) {
        //写入维保分组
        insertMaintenanceGroup(maintenanceItemVos,projectId);
        //写入维保方式
        insertMaintenanceTypeDefine(maintenanceItemVos,projectId);
        Set<String> collect = maintenanceItemVos.stream().map(MaintenanceItemVo::getGroupName).collect(Collectors.toSet());
        //根据名称查询维保分组
        List<MaintenanceGroupWithSubLayer> maintenanceGroupWithSubLayers = maintenanceGroupDao.queryMaintenanceGroupWithName(new ArrayList<>(collect),projectId);
        //拼接维保类型需要的
        Map<String, Long> stringLongMap = queryMaintenanceTypeEnumByNmae();
        //分离新增和编辑的数据
        List<MaintenanceItemVo> maintenanceItemVos1 = queryUpdateData(maintenanceItemVos, transMaintenanceGroupWithLayerToVos(maintenanceGroupWithSubLayers));
        Map<Long, List<MaintenanceItemVo>> maintenanceMap = maintenanceItemVos1.stream().collect(Collectors.groupingBy(MaintenanceItemVo::getGroupId));
        maintenanceMap.forEach((key, val) -> {
            int maxSort = 0;
            for (MaintenanceGroupWithSubLayer maintenanceGroupWithSubLayer : maintenanceGroupWithSubLayers) {
                if (maintenanceGroupWithSubLayer.getId().equals(key)) {
                    maxSort = getMaxSort(maintenanceGroupWithSubLayer.getMaintenanceItemList());
                }

            }
            List<MaintenanceItem> maintenanceItems = new ArrayList<>();
            for (MaintenanceItemVo maintenanceItemVo : val) {
                MaintenanceItem maintenanceItem = new MaintenanceItem();
                maintenanceItem.setContent(maintenanceItemVo.getContent());
                maintenanceItem.setMaintenanceType(stringLongMap.get(maintenanceItemVo.getMaintenanceTypeName()));
                maintenanceItem.setSparePartId(maintenanceItemVo.getSparePartId());
                maintenanceItem.setNumber(maintenanceItemVo.getNumber());
                maintenanceItem.setSort(++maxSort);
                maintenanceItems.add(maintenanceItem);
            }
            maintenanceGroupDao.insertChild(key, maintenanceItems);

        });
    }

    private MaintenanceItemVo isRepeatInFile(List<MaintenanceItemVo> maintenanceItemVos) {
        if (CollectionUtils.isEmpty(maintenanceItemVos) || maintenanceItemVos.size() == 1) {
            return null;
        }
        for (int i = 0; i < maintenanceItemVos.size() - 1; i++) {
            MaintenanceItemVo item = maintenanceItemVos.get(i);
            for (int j = i + 1; j < maintenanceItemVos.size(); j++) {
                MaintenanceItemVo itemNow = maintenanceItemVos.get(j);
                //导入的维保项信息，同一个分组下的维保项目的维保描述、维保项类型和零件类型不能一样
                if (item.getGroupName().equals(itemNow.getGroupName()) && item.getContent().equals(itemNow.getContent())
                        && Objects.equals(item.getMaintenanceTypeName(), itemNow.getMaintenanceTypeName()) && Objects.equals(item.getSparePartId(), itemNow.getSparePartId())) {
                    return item;
                }
            }
        }
        return null;
    }

    private void checkSameNameGroupWhileCreate(String name) {
        LambdaQueryWrapper<MaintenanceGroup> queryWrapper = LambdaQueryWrapper.of(MaintenanceGroup.class);
        queryWrapper.eq(MaintenanceGroup::getName, name);
        queryWrapper.eq(MaintenanceGroup::getProjectId, GlobalInfoUtils.getTenantId());
        MaintenanceGroup existMaintenanceGroup = maintenanceGroupDao.selectOne(queryWrapper);
        Assert.isNull(existMaintenanceGroup, "该项目下已存在同名维保项目组");
    }

    private void checkSameNameGroupWhileEdit(Long id, String name) {
        LambdaQueryWrapper<MaintenanceGroup> queryWrapper = LambdaQueryWrapper.of(MaintenanceGroup.class);
        queryWrapper.eq(MaintenanceGroup::getName, name);
        queryWrapper.eq(MaintenanceGroup::getProjectId, GlobalInfoUtils.getTenantId());
        queryWrapper.ne(MaintenanceGroup::getId, id);
        MaintenanceGroup existMaintenanceGroup = maintenanceGroupDao.selectOne(queryWrapper);
        Assert.isNull(existMaintenanceGroup, "该项目下已存在同名维保项目组");
    }

    private void addGroup(List<MaintenanceItemVo> maintenanceItemVos) {
        if (CollectionUtils.isEmpty(maintenanceItemVos)) {
            return;
        }
        List<Long> itemIds = maintenanceItemVos.stream().map(MaintenanceItemVo::getId).collect(Collectors.toList());
        LambdaQueryWrapper<MaintenanceItem> queryWrapper = LambdaQueryWrapper.of(MaintenanceItem.class)
                .in(MaintenanceItem::getId, itemIds);
        List<MaintenanceGroupWithSubLayer> maintenanceGroupWithSubLayers = maintenanceGroupDao.selectRelatedList(MaintenanceGroupWithSubLayer.class, null, Collections.singletonList(queryWrapper));
        if (CollectionUtils.isEmpty(maintenanceGroupWithSubLayers)) {
            return;
        }
        for (MaintenanceItemVo maintenanceItemVo : maintenanceItemVos) {
            for (MaintenanceGroupWithSubLayer maintenanceGroupWithSubLayer : maintenanceGroupWithSubLayers) {
                List<Long> collect = maintenanceGroupWithSubLayer.getMaintenanceItemList().stream().map(MaintenanceItem::getId).collect(Collectors.toList());
                if (collect.contains(maintenanceItemVo.getId())) {
                    maintenanceItemVo.setGroupId(maintenanceGroupWithSubLayer.getId());
                    maintenanceItemVo.setGroupName(maintenanceGroupWithSubLayer.getName());
                }
            }
        }

    }

    private List<MaintenanceItemVo> transferMaintenanceItemToVo(List<MaintenanceItem> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return Collections.emptyList();
        }
        List<MaintenanceItemVo> resultList = itemList.stream().map(s -> {
            MaintenanceItemVo maintenanceItemVo = new MaintenanceItemVo();
            BeanUtils.copyProperties(s, maintenanceItemVo);
            return maintenanceItemVo;
        }).collect(Collectors.toList());
        Set<Long> sparePartIds = itemList.stream().map(MaintenanceItem::getSparePartId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(sparePartIds)) {
            List<SpareParts> spareParts = sparePartsDao.selectBatchIds(sparePartIds);
            Map<Long, SpareParts> sparePartsMap = spareParts.stream().collect(Collectors.toMap(SpareParts::getId, it -> it));
            resultList.forEach(s -> {
                SpareParts obj = sparePartsMap.get(s.getSparePartId());
                if (Objects.nonNull(obj)) {
                    s.setSparePartName(obj.getName());
                    s.setUnit(obj.getUnit());
                }
            });
        }
        Map<Long, String> maintenanceTypeEnum = queryMaintenanceTypeEnum();
        resultList.forEach(s -> {
            if (Objects.nonNull(s.getMaintenanceType())) {
                s.setMaintenanceTypeName(maintenanceTypeEnum.get(s.getMaintenanceType()));
            }
        });

        return resultList;
    }

    private Map<Long, String> queryMaintenanceTypeEnum() {
        List<MaintenanceTypeDefine> typeDefines = maintenanceTypeDefineDao.queryTypeByName(null, GlobalInfoUtils.getTenantId());
        return typeDefines.stream().collect(Collectors.toMap(BaseEntity::getId, BaseEntity::getName));
    }

    private Map<String, Long> queryMaintenanceTypeEnumByNmae() {
        List<MaintenanceTypeDefine> typeDefines = maintenanceTypeDefineDao.queryTypeByName(null, GlobalInfoUtils.getTenantId());
        return typeDefines.stream().collect(Collectors.toMap(BaseEntity::getName, BaseEntity::getId));
    }
}



