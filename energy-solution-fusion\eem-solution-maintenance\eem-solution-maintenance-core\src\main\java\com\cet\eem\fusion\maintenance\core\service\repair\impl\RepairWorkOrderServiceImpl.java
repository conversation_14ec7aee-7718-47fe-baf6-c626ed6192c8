﻿package com.cet.eem.fusion.maintenance.core.service.repair.impl;

import com.cet.eem.auth.service.AuthUtils;
import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.common.log.service.CommonUtilsService;
import com.cet.eem.bll.common.model.domain.object.organization.Project;
import com.cet.eem.bll.common.model.domain.perception.logicaldevice.PecEventExtendVo;
import com.cet.eem.bll.common.model.domain.subject.huaxingguangdian.EventPlan;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SparePartsReplaceRecordVo;
import com.cet.eem.bll.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.bll.common.util.DataValidationUtils;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.config.MaintenanceConfig;
import com.cet.eem.fusion.maintenance.core.dao.InspectionSchemeDao;
import com.cet.eem.fusion.maintenance.core.dao.PlanSheetDao;
import com.cet.eem.fusion.maintenance.core.dao.WorkOrderCheckInfoDao;
import com.cet.eem.fusion.maintenance.core.dao.WorkOrderDao;
import com.cet.eem.fusion.maintenance.core.dao.devicecomponent.SparePartsDao;
import com.cet.eem.fusion.maintenance.core.dao.repair.RepairWorkOrderDao;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderKeyDef;
import com.cet.eem.fusion.maintenance.core.def.WorkSheetStatusDef;
import com.cet.eem.fusion.maintenance.core.model.EventPlanVo;
import com.cet.eem.fusion.maintenance.core.model.WorkOrderQueryVo;
import com.cet.eem.fusion.maintenance.core.model.maintance.WorkOrderVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.DevicePlanRelationshipSaveVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.MaintenanceContent;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderCountDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderFormSubmitParam;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionCountSearchDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.repair.*;
import com.cet.eem.fusion.maintenance.core.service.WorkOrderService;
import com.cet.eem.fusion.maintenance.core.service.WorkOrderServiceCallBackParam;
import com.cet.eem.fusion.maintenance.core.service.WorkOrderServiceCallBackResult;
import com.cet.eem.fusion.maintenance.core.service.device.SparePartsReplaceRecordService;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectorService;
import com.cet.eem.fusion.maintenance.core.service.maintenance.MaintenanceWorkOrderService;
import com.cet.eem.fusion.maintenance.core.service.repair.RepairWorkOrderService;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.utils.ParamUtils;
import com.cet.eem.common.constant.PecsNodeType;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.def.label.NodeLabelDef;
import com.cet.eem.fusion.common.def.common.SplitCharDef;
import com.cet.eem.common.exception.ValidationException;
import com.cet.eem.fusion.common.utils.file.FileUtils;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.eem.common.model.event.RedisEventKey;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.common.service.RedisService;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.event.dao.PecEventExtendDao;
import com.cet.eem.model.base.ConditionBlock;
import com.cet.eem.model.base.Order;
import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.model.enums.ConfirmEventStatusEnum;
import com.cet.electric.workflow.api.TriggerRestApi;
import com.cet.electric.workflow.api.UserTaskRestApi;
import com.cet.electric.workflow.common.constants.ProcessVariableDefinition;
import com.cet.electric.workflow.common.model.ProcessInstanceResponse;
import com.cet.electric.workflow.common.model.node.config.UserTaskConfig;
import com.cet.electric.workflow.common.model.params.TableTriggerParams;
import com.cet.electric.workflow.common.model.params.UserTaskParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StopWatch;

import java.io.FileInputStream;
import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 4/12/2021
 */
@Service
@Slf4j
public class RepairWorkOrderServiceImpl implements RepairWorkOrderService {

    @Autowired
    WorkOrderDao workOrderDao;

    @Autowired
    RepairWorkOrderDao repairWorkOrderDao;

    @Autowired
    UserTaskRestApi userTaskRestApi;

    @Autowired
    TriggerRestApi triggerRestApi;

    @Autowired
    InspectionSchemeDao inspectionSchemeDao;

    @Autowired
    PlanSheetDao planSheetDao;

    @Autowired
    NodeDao nodeDao;

    @Autowired
    InspectorService inspectorService;

    @Autowired
    AuthUtils authUtils;

    @Autowired
    CommonUtilsService commonUtilsService;

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    WorkOrderCheckInfoDao workOrderCheckInfoDao;

    @Autowired
    WorkOrderService workOrderService;

    @Autowired
    SparePartsReplaceRecordService sparePartsReplaceRecordService;
    @Autowired
    MaintenanceWorkOrderService maintenanceWorkOrderService;
    @Autowired
    SparePartsDao sparePartsDao;
    @Autowired
    MaintenanceConfig maintenanceConfig;
    @Autowired
    RedisService redisService;
    @Value("${cet.eem.work-order.repair.config:''}")
    private String repairConfigPath;

    @Override
    public <T extends InspectionWorkOrderDto> ApiResult<List<T>> queryWorkOrderList(RepairSearchVo dto, Class<T> clazz) {
        // 判断用户所属班组，如果班组用户，那么只允许查询本班组的工单
        return repairWorkOrderDao.queryWorkOrder(dto, GlobalInfoUtils.getUserId(), clazz);
    }

    @Override
    public <T extends InspectionWorkOrderDto> ApiResult<List<T>> queryWorkOrderByNode(RepairByNodeSearchVo dto, Class<T> clazz) {
        // 判断用户所属班组，如果班组用户，那么只允许查询本班组的工单
        return repairWorkOrderDao.queryWorkOrderByNode(dto, clazz);
    }

    @Override
    public List<WorkOrderCountDto> queryWorkOrderCount(InspectionCountSearchDto dto) {
        List<Map<String, Object>> maps = repairWorkOrderDao.queryWorkOrderCountByStatus(dto);
        Map<Integer, String> statusMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.WORK_SHEET_STATUS);
        Map<Integer, WorkOrderCountDto> statusCountMap = new HashMap<>();

        // 解析结果
        for (Map<String, Object> map : maps) {
            WorkOrderCountDto workOrderCountDto = new WorkOrderCountDto();
            Integer status = CommonUtils.parseInteger(map.get(WorkOrderDef.WORKSHEET_STATUS));
            Integer count = CommonUtils.parseInteger(map.get(ColumnDef.COUNT_ID));

            workOrderCountDto.setCount(count);
            workOrderCountDto.setWorkOrderStatus(status);
            workOrderCountDto.setWorkOrderStatusName(statusMap.get(status));
            statusCountMap.put(status, workOrderCountDto);
        }

        if (CollectionUtils.isEmpty(dto.getWorkSheetStatuses())) {
            return new ArrayList<>(statusCountMap.values());
        }

        List<WorkOrderCountDto> result = new ArrayList<>();
        for (Integer workSheetStatus : dto.getWorkSheetStatuses()) {
            WorkOrderCountDto workOrderCountDto = statusCountMap.get(workSheetStatus);
            if (workOrderCountDto == null) {
                workOrderCountDto = new WorkOrderCountDto(workSheetStatus, statusMap.get(workSheetStatus));
            }
            result.add(workOrderCountDto);
        }
        return result;
    }

    @Override
    public ProcessInstanceResponse createWorkOrder(RepairWorkOrderAddDto dto) {
        Long userId = GlobalInfoUtils.getUserId();
        UserVo user = authUtils.queryAndCheckUser(userId);

        return createWorkOrder(dto, user, GlobalInfoUtils.getTenantId());
    }

    /**
     * 加载配置文件
     *
     * @return
     * @throws Exception
     */
    @Override
    public RepairConfigVO getRepairConfig() throws Exception {
        InputStream inputStream;
        if (StringUtils.isNotEmpty(repairConfigPath)) {
            inputStream = new FileInputStream(repairConfigPath);
        } else {
            inputStream = this.getClass().getClassLoader().getResourceAsStream("config/RepairWorkOrderConfig.json");
        }
        if (Objects.isNull(inputStream)) {
            RepairConfigVO repairConfigVO = new RepairConfigVO();
            repairConfigVO.setAddEnable(Boolean.FALSE);
            return repairConfigVO;
        }
        String content = FileUtils.readInputStream(inputStream);
        RepairConfigVO repairConfigVO = JsonTransferUtils.parseString(content, RepairConfigVO.class);
        if (inputStream != null) {
            inputStream.close();
        }
        return repairConfigVO;
    }

    private Long queryTenantId(Long projectId) {
        List<Project> query = modelServiceUtils.query(projectId, NodeLabelDef.PROJECT, Project.class);
        if (CollectionUtils.isNotEmpty(query)) {
            return query.get(0).getTenantid();
        }
        return null;
    }

    private ProcessInstanceResponse createWorkOrder(RepairWorkOrderAddDto dto, UserVo user, Long projectId) {
        if (dto == null) {
            throw new ValidationException("create repairWorkOrder fail");
        }
        Long tenantId = queryTenantId(projectId);
        // 设置工单
        TableTriggerParams tableTriggerParams = new TableTriggerParams();
        tableTriggerParams.setProcessDefinitionKey(WorkOrderKeyDef.REPAIR_KEY);

        Map<String, Object> processVariables = new HashMap<>(CommonUtils.MAP_INIT_SIZE_4);
        processVariables.put(ProcessVariableDefinition.CANDICATE_GROUPS, String.valueOf(dto.getTeamId()));
        processVariables.put(ProcessVariableDefinition.TENANT_ID, tenantId);
        tableTriggerParams.setProcessVariables(processVariables);

        Map<String, Object> formDataList = createWorkOrderByUser(dto, user, projectId, tenantId);
        tableTriggerParams.setFormData(formDataList);

        ApiApiResult<ProcessInstanceResponse> result = workOrderDao.startProcessByTable(user.getId(), tableTriggerParams);
        return result.getData();
    }

    /**
     * 用户手动创建工单
     *
     * @param dto  工单参数
     * @param user 用户信息
     * @return 工单入库数据
     */
    private Map<String, Object> createWorkOrderByUser(RepairWorkOrderAddDto dto, UserVo user, Long projectId, Long tenantId) {
        Map<String, Object> result = new HashMap<>();
        result.put(ColumnDef.MODEL_LABEL, ModelLabelDef.PM_WORK_SHEET);
        result.put(ColumnDef.TASK_TYPE, WorkSheetTaskType.REPAIR);
        result.put(WorkOrderDef.TIME_CONSUME_PLAN, dto.getTimeConsumePlan());
        result.put(WorkOrderDef.TEAM_ID, dto.getTeamId());
        result.put(WorkOrderDef.SOURCE_TYPE, dto.getSourceType());
        result.put(WorkOrderDef.SOURCE_ID, dto.getSourceId());
        result.put(WorkOrderDef.SOURCE_MODEL, dto.getSourceModel());
        if (Objects.nonNull(dto.getEventTime())) {
            RepairSourceIndexVo repairSourceIndexVo = new RepairSourceIndexVo(dto.getDeviceId(), dto.getEventTime(), dto.getPecEventType(), dto.getEventByte(), dto.getCode1(), dto.getCode2());
            result.put(WorkOrderDef.SOURCE_INDEX, JsonTransferUtils.toJSONString(repairSourceIndexVo));
        }
        result.put(ColumnDef.DEVICE_CLASSIFICATION_ID, dto.getDeviceClassificationId());
        result.put(ColumnDef.EVENT_CLASSIFICATION_ID, dto.getEventClassificationId());
        result.put(WorkOrderDef.FAULT_DESCRIPTION, dto.getFaultDescription());
        result.put(ColumnDef.FAULT_SCENARIOS_ID, dto.getFaultScenariosId());
        result.put(WorkOrderDef.TASK_LEVEL, dto.getTaskLevel());
        result.put(WorkOrderDef.WORKSHEET_STATUS, WorkSheetStatusDef.TO_BE_SENT);
        result.put(ColumnDef.PROJECT_ID, projectId);
        result.put(WorkOrderDef.OVER_TIME, false);
        result.put(WorkOrderDef.ATTACHMENT, Objects.isNull(dto.getAttachmentList()) ? null : JsonTransferUtils.toJSONString(dto.getAttachmentList()));
        result.put(WorkOrderDef.DEVICE_PLAN_RELATIONSHIP_MODEL,
                Collections.singletonList(new DevicePlanRelationshipSaveVo(dto.getObjectId(), dto.getObjectLabel())));

        // 创建者信息
        result.put(WorkOrderDef.CREATOR, user.getId());
        long nowTime = System.currentTimeMillis();
        result.put(WorkOrderDef.CREATE_TIME, nowTime);
        result.put(WorkOrderDef.EXECUTE_TIME_PLAN, nowTime);
        result.put(ColumnDef.TENANT_ID, tenantId);
        result.put(WorkOrderDef.INSPECT_TEAM_ID, dto.getInspectTeamId());

        // 工单服务自动生成字段
        result.put(WorkOrderDef.CODE, ProcessVariableDefinition.CODE_SYMBOL);
        // 新加的2个字段
        if (Objects.nonNull(dto.getRepairCategory())) {
            result.put(WorkOrderDef.REPAIR_CATEGORY, dto.getRepairCategory());
        }
        if (Objects.nonNull(dto.getProfessionalCategory())) {
            result.put(WorkOrderDef.PROFESSIONAL_CATEGORY, dto.getProfessionalCategory());
        }
        return result;
    }

    @Override
    public InspectionWorkOrderDto queryWorkOrder(Long workOrderId) {
        if (!ParamUtils.checkPrimaryKeyValid(workOrderId)) {
            throw new ValidationException("工单id不合法!");
        }

        return workOrderDao.queryWorkOrder(workOrderId, InspectionWorkOrderDto.class);
    }

    @Override
    public InspectionWorkOrderDto queryWorkOrder(String code) {
        if (StringUtils.isBlank(code)) {
            throw new ValidationException("工单号不允许为空!");
        }

        return workOrderDao.queryWorkOrder(code, InspectionWorkOrderDto.class);
    }

    @Override
    public UserTaskConfig queryTaskConfig(String code) {
        if (StringUtils.isBlank(code)) {
            throw new ValidationException("工单号不允许为空!");
        }

        InspectionWorkOrderDto workOrder = workOrderService.queryRuntimeWorkOrder(code);
        ApiApiResult<UserTaskConfig> userTaskConfig = userTaskRestApi.getTaskConfig(GlobalInfoUtils.getUserId(), workOrder.getTaskId());
        ParamUtils.checkResultGeneric(userTaskConfig);
        return userTaskConfig.getData();
    }

    @Override
    public void updateWorkOrder(RepairParamsWriteVo repairParamsWriteVo) {
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        InspectionWorkOrderDto workOrder = workOrderService.queryRuntimeWorkOrder(repairParamsWriteVo.getCode());
        Map<String, Object> formData = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        assemblySubmitFormData(repairParamsWriteVo, user, workOrder, formData);

        MaintenanceContent maintenanceContent = repairParamsWriteVo.getMaintenanceContent();
        formData.put(WorkOrderDef.MAINTENANCE_CONTENT, JsonTransferUtils.toJSONString(maintenanceContent));
        workOrderDao.saveModelEntityList(Collections.singletonList(formData));
    }

    @Override
    public void submitInspectParams(RepairParamsWriteVo repairParamsWriteVo) {
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        // 查询工单
        InspectionWorkOrderDto workOrder = workOrderService.queryRuntimeWorkOrder(repairParamsWriteVo.getCode());

        UserTaskParams userTaskParams = new UserTaskParams();
        userTaskParams.setTaskResult(repairParamsWriteVo.getTaskResult());

        Map<String, Object> formData = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        assemblySubmitFormData(repairParamsWriteVo, user, workOrder, formData);

        formData.put(WorkOrderDef.MAINTENANCE_CONTENT, JsonTransferUtils.toJSONString(repairParamsWriteVo.getMaintenanceContent()));
        userTaskParams.setFormData(formData);

        // 先把模型数据更新一份，防止事务
        workOrderDao.saveModelEntityList(Collections.singletonList(formData));

        workOrderDao.submitForm(user.getId(), workOrder.getTaskId(), userTaskParams);
    }

    private void assemblySubmitFormData(RepairParamsWriteVo repairParamsWriteVo, UserVo user, InspectionWorkOrderDto workOrder, Map<String, Object> formData) {
        if (repairParamsWriteVo.getFormData() != null) {
            repairParamsWriteVo.getFormData().forEach(formData::put);
        }

        formData.put(ColumnDef.ID, workOrder.getId());
        formData.put(ColumnDef.MODEL_LABEL, ModelLabelDef.PM_WORK_SHEET);
        formData.put(ColumnDef.FINISH_TIME, repairParamsWriteVo.getFinishTime());
        formData.put(WorkOrderDef.STAFF, user.getId());
        formData.put(WorkOrderDef.EXECUTE_TIME, repairParamsWriteVo.getExecuteTime());
        if (CollectionUtils.isNotEmpty(repairParamsWriteVo.getAttachmentList())) {
            formData.put(WorkOrderDef.ATTACHMENT, JsonTransferUtils.toJSONString(repairParamsWriteVo.getAttachmentList()));
        }
        formData.put(WorkOrderDef.HANDLE_DESCRIPTION, repairParamsWriteVo.getHandleDescription());
    }

    @Override
    public WorkOrderServiceCallBackResult createWorkOrderByInspectWO(WorkOrderServiceCallBackParam param) {
        InspectionWorkOrderDto workOrder = queryWorkOrder(param.getId());
        Assert.notNull(workOrder, "未查询到指定的工单！");
        MaintenanceContent maintenanceContent = JsonTransferUtils.parseObject(workOrder.getMaintenanceContent(), MaintenanceContent.class);
        Assert.notNull(maintenanceContent, "工单内容为空！");
        Assert.notNull(maintenanceContent.getRepairWorkOrderAddDto(), "创建维修工单元数据为空！");
        UserVo user = authUtils.queryAndCheckUser(workOrder.getStaff());
        ProcessInstanceResponse repairWorkOrder = createWorkOrder(maintenanceContent.getRepairWorkOrderAddDto(), user, workOrder.getProjectId());
        Assert.notNull(repairWorkOrder, "创建维修工单失败！");
        Map<String, Object> formData = (Map<String, Object>) repairWorkOrder.getFormData();
        Map<String, Object> obj = new HashMap<>();
        obj.put(ColumnDef.ID, workOrder.getId());
        obj.put(ColumnDef.MODEL_LABEL, ModelLabelDef.PM_WORK_SHEET);
        obj.put(WorkOrderDef.RELATED_CODE_ID, formData.get(ColumnDef.ID));
        obj.put(WorkOrderDef.RELATED_CODE, formData.get(ColumnDef.CODE));
        WorkOrderServiceCallBackResult result = new WorkOrderServiceCallBackResult(Collections.singletonList(obj));
        log.info("根据巡检工单创建维修工单返回结果：{}", JsonTransferUtils.toJSONString(result));
        return result;
    }

    @Override
    public WorkOrderServiceCallBackResult saveMaintenanceContent(WorkOrderServiceCallBackParam param) {
        InspectionWorkOrderDto workOrder = queryWorkOrder(param.getId());
        Assert.notNull(workOrder, "未查询到指定的工单！");
        MaintenanceContent maintenanceContent = JsonTransferUtils.parseObject(workOrder.getMaintenanceContent(), MaintenanceContent.class);
        Map<String, Object> formData = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        formData.put(ColumnDef.ID, workOrder.getId());
        formData.put(ColumnDef.MODEL_LABEL, ModelLabelDef.PM_WORK_SHEET);
        formData.put(WorkOrderDef.MAINTENANCE_CONTENT, JsonTransferUtils.toJSONString(maintenanceContent));

        handleMaintenanceContent(maintenanceContent, workOrder, formData);
        return new WorkOrderServiceCallBackResult(Collections.singletonList(formData));
    }

    private void handleMaintenanceContent(MaintenanceContent maintenanceContent, InspectionWorkOrderDto workOrder, Map<String, Object> formData) {
        if (maintenanceContent != null) {
            List<SparePartsReplaceRecordVo> sparePartsReplaceRecords = maintenanceContent.getSparePartsReplaceRecords();
            if (CollectionUtils.isNotEmpty(sparePartsReplaceRecords)) {
                sparePartsReplaceRecords.forEach(it -> it.setWorkOrderId(workOrder.getId()));
                sparePartsReplaceRecordService.writeSparePartsReplaceRecord(sparePartsReplaceRecords, workOrder.getId());
            }

            EventPlan eventPlan = writeEventPlan(maintenanceContent.getEventPlan(), workOrder.getFaultScenariosId());
            if (eventPlan != null) {
                maintenanceContent.getEventPlan().setEventPlanId(eventPlan.getId());
                formData.put(WorkOrderDef.EVENT_PLAN_ID, eventPlan.getId());
            }
        }
    }

    /**
     * 根据名称查询预案
     *
     * @param name 预案名称
     * @return 预案
     */
    private List<EventPlan> queryEventPlan(String name) {
        QueryCondition condition = new QueryConditionBuilder<>(ModelLabelDef.EVENT_PLAN)
                .eq(ColumnDef.NAME, name)
                .eq(ColumnDef.PROJECT_ID, GlobalInfoUtils.getTenantId())
                .build();
        return modelServiceUtils.query(condition, EventPlan.class);
    }

    private EventPlan writeEventPlan(EventPlanVo eventPlanVo, Long faultScenariosId) {
        if (eventPlanVo == null || !ParamUtils.checkPrimaryKeyValid(faultScenariosId)) {
            return null;
        }

        // 已经有的预案
        if (ParamUtils.checkPrimaryKeyValid(eventPlanVo.getEventPlanId())) {
            List<EventPlan> eventPlans = modelServiceUtils.query(eventPlanVo.getEventPlanId(), ModelLabelDef.EVENT_PLAN, EventPlan.class);
            if (CollectionUtils.isEmpty(eventPlans)) {
                throw new ValidationException("预案不存在！");
            }

            EventPlan eventPlan = eventPlans.get(0);
            eventPlan.setAdoptNumber(eventPlan.getAdoptNumber() + 1);
            List<EventPlan> eventPlans1 = modelServiceUtils.writeData(eventPlans, EventPlan.class);
            return eventPlans1.get(0);
        }

        List<EventPlan> oldEventPlans = queryEventPlan(eventPlanVo.getEventPlanName());
        // 新预案
        EventPlan eventPlan = new EventPlan();
        eventPlan.setProjectId(eventPlanVo.getProjectId());
        eventPlan.setName(eventPlanVo.getEventPlanName());
        eventPlan.setSolution(eventPlanVo.getSolution());
        eventPlan.setAdoptNumber(1);
        DataValidationUtils.checkNameRepeatForBaseVo(oldEventPlans, Collections.singletonList(eventPlan));
        List<EventPlan> eventPlans = modelServiceUtils.writeData(eventPlan, EventPlan.class);
        modelServiceUtils.writeRelations(new BaseVo(faultScenariosId, ModelLabelDef.FAULT_SCENARIOS), JsonTransferUtils.transferList(eventPlans, BaseVo.class));
        return eventPlans.get(0);
    }

    @NotNull
    private WorkOrderQueryVo getOverTimeWorkOrderQueryVo(LocalDateTime cutOffTime, int batch) {
        Integer batchHandleCount = maintenanceConfig.getBatchHandleCount();
        WorkOrderQueryVo workOrderQueryVo = new WorkOrderQueryVo();
        workOrderQueryVo.setWorkOrderStatus(Arrays.asList(WorkSheetStatusDef.TO_BE_SENT, WorkSheetStatusDef.ALREADY_SENT));
        workOrderQueryVo.setTaskType(WorkSheetTaskType.REPAIR);
        workOrderQueryVo.setStartTime(TimeUtil.addDateTimeByCycle(cutOffTime, AggregationCycle.ONE_MONTH,
                -1 * maintenanceConfig.getInspect().getCheckOverTime().getHandleOverTimeWoPreMonth()));
        workOrderQueryVo.setEndTime(cutOffTime);
        workOrderQueryVo.setPage(new Page(batch * batchHandleCount, (batch + 1) * batchHandleCount));
        Order order = new Order(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.ASC, 1);
        workOrderQueryVo.setOrders(Collections.singletonList(order));
        workOrderQueryVo.setUserId(PecsNodeType.ROOT_USER_ID);
        return workOrderQueryVo;
    }

    @Override
    public void updateOverTimeStatus() {
        log.info("开始执行更新超时维修工单状态任务！");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
//        List<InspectionWorkOrderDto> workOrderDtoList = workOrderDao.queryWorkOrderByWorkStatus(Arrays.asList(WorkSheetStatusDef.TO_BE_SENT, WorkSheetStatusDef.ALREADY_SENT),
//                WorkSheetTaskType.REPAIR, null, null, null, InspectionWorkOrderDto.class);
        LocalDateTime now = LocalDateTime.now();
        List<Long> overTimeWoIds = new ArrayList<>();
        int batch = 0;
        do {
            WorkOrderQueryVo workOrderQueryVo = getOverTimeWorkOrderQueryVo(now, batch++);
            List<InspectionWorkOrderDto> workOrderDtoList = workOrderDao
                    .queryWorkOrderByWorkStatusByPage(workOrderQueryVo, InspectionWorkOrderDto.class)
                    .getData();
            if (CollectionUtils.isEmpty(workOrderDtoList)) {
                break;
            }

            List<Map<String, Object>> result = overTimeWorkOrders(workOrderDtoList, now);
            workOrderDtoList.forEach(it -> overTimeWoIds.add(it.getId()));
            log.debug("执行即将超时维修工单提醒任务，本次推送工单信息为：{}", JsonTransferUtils.toJSONString(result));
        } while (true);

        stopWatch.stop();
        log.info("完成执行更新超时维修工单状态任务，本次更新信息为：{}，耗时{}ms", overTimeWoIds, stopWatch.getTotalTimeMillis());
    }

    @Autowired
    PecEventExtendDao pecEventExtendDao;


    @Override
    public void updateSystemStatus(WorkOrderFormSubmitParam param, Long userId) {
        WorkOrderVo workOrderVo = workOrderDao.queryWorkOrder(param.getCode(), WorkOrderVo.class);
        if (Objects.isNull(workOrderVo)) {
            return;
        }
        RepairSourceIndexVo repairSourceIndexVo = JsonTransferUtils.parseString(workOrderVo.getSourceIndex(), RepairSourceIndexVo.class);
        if (Objects.isNull(repairSourceIndexVo)) {
            return;
        }
        PecEventExtendVo event = pecEventExtendDao.queryPecEventExtendList(repairSourceIndexVo.getEventTime(), repairSourceIndexVo.getDeviceId(), repairSourceIndexVo.getPecEventType(),
                repairSourceIndexVo.getCode1(), repairSourceIndexVo.getCode2(), repairSourceIndexVo.getEventByte());
        if (Objects.isNull(event)) {
            return;
        }
        // 修改该peceventvo的状态，并且写入一条数据到redis中
        event.setConfirmeventstatus(ConfirmEventStatusEnum.CONFIRMED.getCode());
        event.setOperator(userId);
        pecEventExtendDao.saveOrUpdateBatch(Collections.singletonList(event));
        String key = event.getDeviceid() + SplitCharDef.UNDERLINE + ColumnDef.DEVICE + SplitCharDef.UNDERLINE +
                event.getEventtime() + SplitCharDef.UNDERLINE + event.getPeceventtype() + SplitCharDef.UNDERLINE + event.getPeceventlevel();
        redisService.stringAddValue(RedisEventKey.EVENT_KEY_PREFIX + key,
                JsonTransferUtils.toJSONString(ConfirmEventStatusEnum.CONFIRMED.getCode()), 1, TimeUnit.DAYS);


    }

    @NotNull
    private List<Map<String, Object>> overTimeWorkOrders(List<InspectionWorkOrderDto> workOrderDtoList, LocalDateTime now) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (InspectionWorkOrderDto workOrder : workOrderDtoList) {
            if (BooleanUtils.isTrue(workOrder.getOvertime())) {
                continue;
            }

            long duration = Duration.between(workOrder.getCreateTime(), now).toMillis();
            if (duration > workOrder.getTimeConsumePlan()) {
                Map<String, Object> map = new HashMap<>(CommonUtils.MAP_INIT_SIZE_4);
                map.put(ColumnDef.ID, workOrder.getId());
                map.put(WorkOrderDef.OVER_TIME, true);
                map.put(ColumnDef.MODEL_LABEL, ModelLabelDef.PM_WORK_SHEET);
                result.add(map);
            }
        }

        if (CollectionUtils.isNotEmpty(result)) {
            workOrderDao.saveModelEntityList(result);
            if (log.isInfoEnabled()) {
                log.info("save modelEntityList size:{}", result.size());
            }
        }
        return result;
    }
}


