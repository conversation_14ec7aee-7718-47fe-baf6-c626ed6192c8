﻿package com.cet.eem.fusion.maintenance.core.model.workorder.repair;

import com.cet.eem.bll.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.fusion.common.model.Page;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 4/12/2021
 */
@Getter
@Setter
@ApiModel(description = "巡检工单查询条件")
public class RepairSearchVo {
    @ApiModelProperty("开始时间")
    @NotNull(message = "开始时间不允许为空！")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    @NotNull(message = "结束时间不允许为空！")
    private LocalDateTime endTime;

    @ApiModelProperty("工单号，模糊匹配")
    private String keyword;

    @ApiModelProperty("班组")
    private Long teamId;

    @ApiModelProperty("工单状态")
    private Integer workSheetStatus;

    @ApiModelProperty("是否只看超时工单，为null表示查询所有，true表示只看超时，false表示只看未超时")
    private Boolean overTimeOnly;

    @ApiModelProperty("分页")
    private Page page;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("是否为巡检用户")
    private boolean isInspectUser;

    @ApiModelProperty("任务等级")
    @JsonProperty(WorkOrderDef.TASK_LEVEL)
    private Integer taskLevel;
}


