﻿package com.cet.eem.fusion.maintenance.core.model.devicemanage.template;

import com.cet.eem.fusion.common.model.Page;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @Author: 骆海瑞
 * @Description: 查询设备模版定于入参
 * @Data: Created in 2025-04-17
 */
@Getter
@Setter
public class QueryTemplatesSearchVO {

    /**
     * 模版id
     */
    @NotNull(message = "模版id不能为空")
    private Long id;

    /**
     * 分页参数
     */
    @NotNull(message = "分页参数不能为空")
    private Page page;
}


