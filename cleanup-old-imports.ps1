# Clean up old imports that are no longer needed
Write-Host "Cleaning up old imports..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

# Old imports to remove
$oldImports = @(
    "import com.cet.eem.fusion.common.entity.Result;",
    "import com.cet.eem.fusion.common.entity.ResultWithTotal;",
    "import com.cet.eem.bll.common.entity.Result;",
    "import com.cet.eem.bll.common.entity.ResultWithTotal;"
)

function Clean-OldImports {
    param(
        [string]$filePath
    )
    
    if (!(Test-Path $filePath)) {
        return $false
    }
    
    $content = Get-Content $filePath -Raw -Encoding UTF8
    $originalContent = $content
    $changed = $false
    
    foreach ($oldImport in $oldImports) {
        if ($content -match [regex]::Escape($oldImport)) {
            $content = $content -replace [regex]::Escape($oldImport), ""
            $changed = $true
            Write-Host "  - Removed: $oldImport" -ForegroundColor Yellow
        }
    }
    
    # Clean up empty lines that might be left after removing imports
    if ($changed) {
        # Remove multiple consecutive empty lines and replace with single empty line
        $content = $content -replace "(\r?\n){3,}", "`n`n"
        Set-Content $filePath -Value $content -Encoding UTF8
        Write-Host "✓ Cleaned imports in: $filePath" -ForegroundColor Green
        return $true
    }
    
    return $false
}

# Process all Java files
Write-Host "Processing Java files to clean old imports..." -ForegroundColor Cyan
$javaFiles = Get-ChildItem -Path $coreSourcePath -Filter "*.java" -Recurse
$filesUpdated = 0

foreach ($file in $javaFiles) {
    if (Clean-OldImports -filePath $file.FullName) {
        $filesUpdated++
    }
}

Write-Host "`nOld import cleanup completed!" -ForegroundColor Green
Write-Host "Files updated: $filesUpdated" -ForegroundColor Cyan
