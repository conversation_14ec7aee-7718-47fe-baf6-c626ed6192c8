﻿package com.cet.eem.fusion.maintenance.core.controller.maintenance;

import com.cet.eem.fusion.maintenance.core.controller.bff.maintenance.MaintenanceItemBffController;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName : InspectionPlanController
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-22 09:19
 */
@Api(value = "/eem/v1/maintenance", tags = "维保：维保项目管理")
@RequestMapping(value = "/eem/solution/maintenance/eem/v1/maintenance")
@RestController
@Validated
public class MaintenanceController extends MaintenanceItemBffController {

}


