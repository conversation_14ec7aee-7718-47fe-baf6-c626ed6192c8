﻿package com.cet.eem.fusion.maintenance.core.schedule.event;

import com.cet.eem.bll.common.log.constant.EEMOperationLogType;
import com.cet.eem.bll.common.log.service.CommonUtilsService;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectionPlanService;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectionWorkOrderService;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.common.definition.LoginDef;
import com.cet.eem.common.util.JsonUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName : GenerateInspectionOrderHandler
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-26 08:45
 */
@Component
@Slf4j
public class GenerateInspectionOrderHandler implements ChannelAwareMessageListener {

    @Autowired
    InspectionWorkOrderService inspectionWorkOrderService;

    @Autowired
    CommonUtilsService commonUtilsService;

    @Autowired
    InspectionPlanService inspectionPlanService;

    @Autowired
    ObjectMapper objectMapper;

    @Override
    public void onMessage(Message message, Channel channel) {
        CreateOrderCommand createInspectionOrderCommand = null;
        try {
            createInspectionOrderCommand = getObjectFromMessage(message);
            inspectionWorkOrderService.createWorkOrderByPlanSheet(createInspectionOrderCommand);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            commonUtilsService.writeAddOperationLogs(EEMOperationLogType.INSPECTOR_WORK_ORDER, "消费消息队列生成巡检工单！" + CommonUtils.getIP(), createInspectionOrderCommand, LoginDef.USER_SYSTEM_ID);
            inspectionPlanService.editUsed(createInspectionOrderCommand.getId());
        } catch (Exception e) {
            log.error("创建巡检工单失败：", e);
            if(createInspectionOrderCommand != null) {
                commonUtilsService.writeAddOperationLogs(EEMOperationLogType.INSPECTOR_WORK_ORDER, "消费消息队列生成巡检工单失败：" + CommonUtils.getIP(), createInspectionOrderCommand, LoginDef.USER_SYSTEM_ID);
            }
        }
    }

    private CreateOrderCommand getObjectFromMessage(Message message) {
        byte[] body = message.getBody();
        return JsonUtil.parseObject(new String(body), CreateOrderCommand.class);
    }
}


