﻿package com.cet.eem.fusion.maintenance.core.dao.devicemanager.impl;

import com.cet.eem.bll.common.model.domain.object.powersystem.DeviceCommonInfo;
import com.cet.eem.fusion.maintenance.core.dao.devicemanager.TechParamValueDao;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.EquipmentSearchDto;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.TechParamValue;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import com.cet.eem.model.base.ConditionBlock;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import com.cet.eem.toolkit.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-17
 */
@Service
public class TechParamValueDaoImpl extends ModelDaoImpl<TechParamValue> implements TechParamValueDao {
    @Autowired
    private ModelServiceUtils modelServiceUtils;

    @Override
    public List<TechParamValue> queryTechParam(EquipmentSearchDto searchDto) {
        LambdaQueryWrapper<TechParamValue> wrapper = LambdaQueryWrapper.of(TechParamValue.class)
                .eq(TechParamValue::getObjectId, searchDto.getEquipmentId())
                .eq(TechParamValue::getObjectLabel, searchDto.getEquipmentLabel());
        return this.selectList(wrapper);
    }

    @Override
    public List<TechParamValue> queryTechParam(List<BaseVo> baseVos) {
        int group = 1;
        QueryConditionBuilder<BaseEntity> builder = new QueryConditionBuilder<>(ModelLabelDef.TECH_PARAM_DATA)
                .composeMethod(true);
        if (CollectionUtils.isEmpty(baseVos)) {
            return Collections.EMPTY_LIST;
        }
        for (BaseVo baseVo : baseVos) {
            builder.where(ColumnDef.C_OBJECT_ID, ConditionBlock.OPERATOR_EQ, baseVo.getId(), group)
                    .where(ColumnDef.C_OBJECT_Label, ConditionBlock.OPERATOR_EQ, baseVo.getModelLabel(), group);
            group++;
        }
        return modelServiceUtils.query(builder.build(), TechParamValue.class);
    }

    @Override
    public List<TechParamValue> queryTechParamWith(List<TechParamValue> techParamValues) {
        int group = 1;
        QueryConditionBuilder<BaseEntity> builder = new QueryConditionBuilder<>(ModelLabelDef.TECH_PARAM_DATA)
                .composeMethod(true);
        if (CollectionUtils.isEmpty(techParamValues)) {
            return Collections.EMPTY_LIST;
        }
        for (TechParamValue values : techParamValues) {
            builder.where(ColumnDef.C_OBJECT_ID, ConditionBlock.OPERATOR_EQ, values.getObjectId(), group)
                    .where(ColumnDef.C_OBJECT_Label, ConditionBlock.OPERATOR_EQ, values.getObjectLabel(), group)
                    .where(ColumnDef.TECHPARAM_TEMPLATE_ID, ConditionBlock.OPERATOR_EQ, values.getTechParamTemplateId(), group);
            group++;
        }
        return modelServiceUtils.query(builder.build(), TechParamValue.class);
    }
}


