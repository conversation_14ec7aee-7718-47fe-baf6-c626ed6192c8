﻿package com.cet.eem.fusion.maintenance.core.config;

import com.cet.eem.fusion.maintenance.core.schedule.event.GenerateInspectionOrderHandler;
import com.cet.eem.fusion.maintenance.core.schedule.event.GenerateMaintenanceOrderHandler;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName : RabbitMqConfig
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-30 10:33
 */
@Configuration
public class MaintenanceRabbitMqConfig {

    public static final String ORDER_EXCHANGE = "order_exchange";

    public static final String INSPECTION_ORDER = "inspection_order";

    public static final String MAINTENANCE_ORDER = "maintenance_order";

    public static final String INSPECTION_ORDER_LISTENER = "inspection_order_listener";

    public static final String MAINTENANCE_ORDER_LISTENER = "maintenance_order_listener";

    @Bean(name = INSPECTION_ORDER)
    public Queue createInspectionOrderQueue() {
        return new Queue(INSPECTION_ORDER, true);
    }

    @Bean(name = MAINTENANCE_ORDER)
    public Queue createMaintenanceOrderQueue() {
        return new Queue(MAINTENANCE_ORDER, true);
    }

    @Bean(name = ORDER_EXCHANGE)
    public TopicExchange createOrderExchange() {
        return new TopicExchange(ORDER_EXCHANGE);
    }

    @Bean
    public Binding bindingInspectionQueueToExchange(@Qualifier(INSPECTION_ORDER) Queue inspectionOrderQueue, @Qualifier(ORDER_EXCHANGE) TopicExchange topicExchange) {
        return BindingBuilder.bind(inspectionOrderQueue).to(topicExchange).with(INSPECTION_ORDER);
    }

    @Bean
    public Binding bindingMaintenanceQueueToExchange(@Qualifier(MAINTENANCE_ORDER) Queue maintenanceOrderQueue, @Qualifier(ORDER_EXCHANGE) TopicExchange topicExchange) {
        return BindingBuilder.bind(maintenanceOrderQueue).to(topicExchange).with(MAINTENANCE_ORDER);
    }

    @Bean(name = INSPECTION_ORDER_LISTENER)
    public SimpleMessageListenerContainer createInspectionOrderMessageListenerContainer(ConnectionFactory connectionFactory, GenerateInspectionOrderHandler generateInspectionOrderHandler) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setQueueNames(INSPECTION_ORDER);
        container.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        container.setMessageListener(generateInspectionOrderHandler);
        return container;
    }

    @Bean(name = MAINTENANCE_ORDER_LISTENER)
    public SimpleMessageListenerContainer createMaintenanceOrderMessageListenerContainer(ConnectionFactory connectionFactory, GenerateMaintenanceOrderHandler generateMaintenanceOrderHandler) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setQueueNames(MAINTENANCE_ORDER);
        container.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        container.setMessageListener(generateMaintenanceOrderHandler);
        return container;
    }
}

