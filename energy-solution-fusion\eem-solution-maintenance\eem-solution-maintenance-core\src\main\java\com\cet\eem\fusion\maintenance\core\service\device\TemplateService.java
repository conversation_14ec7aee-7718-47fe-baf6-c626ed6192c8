﻿package com.cet.eem.fusion.maintenance.core.service.device;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.MeasureNode;
import com.cet.eem.bll.common.model.node.EemNodeFieldInfo;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.template.*;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.commons.ApiResult;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-11
 */
public interface TemplateService {
    /**
     * 返回模板节点树
     *
     * @return
     */
    List<EquipmentNodeTreeVo> queryTemplateNodeTree();


    /**
     * 插入节点
     *
     * @param data
     * @return
     */
    EquipmentNodeTreeDto insertTemplateNodeTree(EquipmentNodeTreeDto data);


    /**
     * 更新节点
     *
     * @param data
     * @return
     */
    EquipmentNodeTreeDto editTemplateNodeTree(EquipmentNodeTreeDto data);


    /**
     * 删除模板节点
     *
     * @param ids
     */
    void deleteNode(List<Long> ids);


    /**
     * 写入模板
     *
     * @param template
     * @param parentId
     */
    void writeTemplate(AttributeTemplate template, Long parentId);


    /**
     * 查询节点树下的模板
     *
     * @param id
     * @return
     */
    List<AttributeTemplateVo> queryTemplates(Long id);

    /**
     * 查询节点树下的模板
     *
     * @param searchV0
     * @return
     */
    ApiResult<List<AttributeTemplateVo>> queryTemplates(QueryTemplatesSearchVO searchV0);


    /**
     * 删除模板
     *
     * @param id
     */
    void deleteTemplate(Long id, Long parentId);


    /**
     * 查看运行参数节点树
     *
     * @return
     */
    List<RunningParamNodeVo> queryRunningParamNodeTree();


    List<MeasureNode> queryRunningParam(Long id);


    /**
     * 更新模板
     *
     * @param template
     * @param parentId
     */
    void updateTemplate(AttributeTemplate template, Long parentId);

    AttributeTemplate getTemplate(Long templateId);

    /**
     * 查询所有的模板节点树，并且带有模板配置本身
     *
     * @return
     */
    List<EquipmentNodeTreeVo> queryAllTemplateNodeTree();

    /**
     * 查询节点模板以及其父节点
     *
     * @param projectId
     * @param nodeTemplateId
     * @return
     */
    Map<Long, List<BaseVo>> queryNodeTemplateWithParentNode(Long projectId, Collection<Long> nodeTemplateId);

    Map<Long, String> queryNodeTemplateParentPath(Long projectId, Collection<Long> nodeTemplateId);

    /**
     * 查询节点详情数据带有模板信息
     *
     * @param node
     * @return
     */
    List<EemNodeFieldInfo> queryNodeInfoByNodeFieldDefWithTemplate(BaseVo node);
}


