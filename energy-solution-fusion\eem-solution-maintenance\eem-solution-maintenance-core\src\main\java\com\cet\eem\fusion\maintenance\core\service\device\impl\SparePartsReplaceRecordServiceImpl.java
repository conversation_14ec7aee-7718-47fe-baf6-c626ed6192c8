﻿package com.cet.eem.fusion.maintenance.core.service.device.impl;

import com.cet.eem.fusion.maintenance.core.dao.devicecomponent.SparePartsDao;
import com.cet.eem.fusion.maintenance.core.dao.devicecomponent.SparePartsReplaceRecordDao;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SpareParts;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SparePartsReplaceRecord;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SparePartsReplaceRecordVo;
import com.cet.eem.fusion.maintenance.core.service.device.SparePartsReplaceRecordService;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/9
 */
@Service
public class SparePartsReplaceRecordServiceImpl implements SparePartsReplaceRecordService {
    private final ModelServiceUtils modelServiceUtils;

    private final SparePartsReplaceRecordDao sparePartsReplaceRecordDao;

    private final SparePartsDao sparePartsDao;

    public SparePartsReplaceRecordServiceImpl(ModelServiceUtils modelServiceUtils,
                                              SparePartsReplaceRecordDao sparePartsReplaceRecordDao,
                                              SparePartsDao sparePartsDao) {
        this.modelServiceUtils = modelServiceUtils;
        this.sparePartsReplaceRecordDao = sparePartsReplaceRecordDao;
        this.sparePartsDao = sparePartsDao;
    }

    @Override
    public void writeSparePartsReplaceRecord(List<? extends SparePartsReplaceRecord> data, Long workOrderId) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }

        // 根据工单查询已经入库的数据
        List<SparePartsReplaceRecord> oldReplaceRecords = sparePartsReplaceRecordDao.queryByWorkOderId(Collections.singletonList(workOrderId));

        // 查询备件所属的备件系统
        Set<Long> storageIds = data.stream().map(SparePartsReplaceRecord::getSparePartsStorageId).collect(Collectors.toSet());
        List<Map<String, Object>> storageListMap = sparePartsDao.querySparePartsStorageWithSystem(storageIds);
        List<BaseVo> storageList = JsonTransferUtils.transferList(storageListMap, BaseVo.class);
        Map<Long, Long> sparePartsStorageWithSystemMap = new HashMap<>();
        for (BaseVo baseVo : storageList) {
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                sparePartsStorageWithSystemMap.put(baseVo.getId(), baseVo.getChildren().get(0).getId());
            }
        }

        // 校验数据，并入库
        long nowTime = System.currentTimeMillis();
        for (SparePartsReplaceRecord sparePartsReplaceRecord : data) {
            Assert.notNull(sparePartsReplaceRecord.getNumber(), "备件数量不允许为空！");
            Assert.notNull(sparePartsReplaceRecord.getObjectId(), "设备id不允许为空！");
            Assert.isTrue(StringUtils.isNotBlank(sparePartsReplaceRecord.getObjectLabel()), "设备标识不允许为空！");
            Assert.notNull(sparePartsReplaceRecord.getSparePartsStorageId(), "备件id不允许为空！");
            sparePartsReplaceRecord.setWorkOrderId(workOrderId);

            sparePartsReplaceRecord.setDeviceSystemId(sparePartsStorageWithSystemMap.get(sparePartsReplaceRecord.getSparePartsStorageId()));
            if (sparePartsReplaceRecord.getLogtime() == null) {
                sparePartsReplaceRecord.setLogtime(nowTime);
            }
        }

        if (CollectionUtils.isEmpty(oldReplaceRecords)) {
            modelServiceUtils.writeData(data);
            return;
        }

        List<Long> deleteIds = new ArrayList<>();
        for (SparePartsReplaceRecord oldReplaceRecord : oldReplaceRecords) {
            Optional<? extends SparePartsReplaceRecord> any = data.stream().filter(it -> isaBoolean(oldReplaceRecord, it)).findAny();
            if (!any.isPresent()) {
                deleteIds.add(oldReplaceRecord.getId());
            }
        }

        for (SparePartsReplaceRecord spRecord : data) {
            Optional<SparePartsReplaceRecord> any = oldReplaceRecords.stream().filter(it -> isaBoolean(it, spRecord)).findAny();
            any.ifPresent(sparePartsReplaceRecord -> spRecord.setId(sparePartsReplaceRecord.getId()));
        }

        modelServiceUtils.delete(ModelLabelDef.SPAREPARTS_REPLACERECORD, deleteIds);
        modelServiceUtils.writeData(data);
    }

    private boolean isaBoolean(SparePartsReplaceRecord oldReplaceRecord, SparePartsReplaceRecord it) {
        return Objects.equals(oldReplaceRecord.getObjectId(), it.getObjectId()) &&
                Objects.equals(oldReplaceRecord.getObjectLabel(), it.getObjectLabel())
                && Objects.equals(oldReplaceRecord.getSparePartsStorageId(), it.getSparePartsStorageId())
                && Objects.equals(oldReplaceRecord.getWorkOrderId(), it.getWorkOrderId())
                && Objects.equals(oldReplaceRecord.getDeviceSystemId(), it.getDeviceSystemId());
    }

    @Override
    public List<SparePartsReplaceRecordVo> querySparePartsReplaceRecordByWorkOrders(@NotNull Collection<Long> wordOrderIds) {
        List<SparePartsReplaceRecord> records = sparePartsReplaceRecordDao.queryByWorkOderId(wordOrderIds);

        //根据备件id查备件名称

        Set<Long> collect = records.stream()
                .map(SparePartsReplaceRecord::getSparePartsStorageId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        List<SpareParts> spareParts = sparePartsDao.selectBatchIds(collect);

        List<SparePartsReplaceRecordVo> result = JsonTransferUtils.transferList(records, SparePartsReplaceRecordVo.class);
        assemblyRecord(result, spareParts);

        return result;
    }

    /**
     * 组装更换记录数据
     *
     * @param sparePartsReplaceRecords
     * @param spareParts
     */
    private void assemblyRecord(List<SparePartsReplaceRecordVo> sparePartsReplaceRecords, List<SpareParts> spareParts) {
        if (CollectionUtils.isEmpty(sparePartsReplaceRecords) || CollectionUtils.isEmpty(spareParts)) {
            return;
        }

        for (SparePartsReplaceRecordVo item : sparePartsReplaceRecords) {
            SpareParts itemNow = spareParts.stream().filter(it -> Objects.equals(item.getSparePartsStorageId(), it.getId())).findAny().orElse(null);
            if (itemNow == null) {
                continue;
            }

            item.setSparePartsName(itemNow.getName());
            item.setModel(itemNow.getModel());
            item.setBrand(itemNow.getBrand());
            item.setUnit(itemNow.getUnit());
        }
    }


}


