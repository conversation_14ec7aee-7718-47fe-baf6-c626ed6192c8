﻿package com.cet.eem.fusion.maintenance.core.model.devicemanage;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import lombok.Data;

/**
 * 列头柜
 *
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-27
 */
@Data
@ModelLabel(ModelLabelDef.ARRAY_CABINET)
public class ArrayCabiNetVo extends EntityWithName {

    public ArrayCabiNetVo() {
        this.modelLabel = ModelLabelDef.ARRAY_CABINET;
    }
}


