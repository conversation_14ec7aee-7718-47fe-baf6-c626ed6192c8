# 运维插件迁移计划

## 项目概述
- **源模块**: energy-base/cet-eem-maintenance (430个Java文件)
- **目标插件**: energy-solution-fusion/eem-solution-maintenance
- **迁移类型**: 完整业务逻辑迁移，包名更新

## 迁移状态

### ✅ 阶段1: 项目结构设置 (已完成)
- [x] 创建主插件目录结构
- [x] 创建主POM文件
- [x] 创建核心模块POM
- [x] 创建服务模块POM  
- [x] 创建基础配置文件

### 🔄 阶段2: 核心模块迁移 (进行中)
- [ ] 迁移config包 (4个文件)
- [ ] 迁移constant包 (3个文件)
- [ ] 迁移dao包及子包 (约150个文件)
- [ ] 迁移def包 (约15个文件)
- [ ] 迁移listener包 (3个文件)
- [ ] 迁移model包及子包 (约100个文件)
- [ ] 迁移schedule包及子包 (约50个文件)
- [ ] 迁移service包及子包 (约80个文件)
- [ ] 迁移utils包 (6个文件)

### ⏳ 阶段3: 服务模块迁移 (待开始)
- [ ] 迁移控制器文件
- [ ] 创建启动类
- [ ] 更新请求映射

### ⏳ 阶段4: 配置和集成 (待开始)
- [ ] 创建自动配置类
- [ ] 创建插件配置
- [ ] 创建Swagger配置

### ⏳ 阶段5: 测试和验证 (待开始)
- [ ] 验证所有文件迁移
- [ ] 检查依赖关系
- [ ] 验证配置文件

## 包名映射规则
- **源包**: `com.cet.eem.bll.maintenance.*`
- **目标包**: `com.cet.eem.fusion.maintenance.core.*`

## 当前任务
正在进行核心模块的系统性迁移，需要处理430个Java文件的包名更新和结构重组。
