﻿package com.cet.eem.fusion.maintenance.core.dao.maintenance;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.MaintenanceTypeDefine;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/13
 */
public interface MaintenanceTypeDefineDao extends BaseModelDao<MaintenanceTypeDefine> {
    /**
     * 根据维保类型名称查询维保类型
     *
     * @param names
     * @param projectId
     * @return
     */
    List<MaintenanceTypeDefine> queryTypeByNames(Collection<String> names, Long projectId);

    /**
     * 根据维保类型名称关键字查询维保类型
     *
     * @param key
     * @param projectId
     * @return
     */
    List<MaintenanceTypeDefine> queryTypeByName(String key, Long projectId);
}


