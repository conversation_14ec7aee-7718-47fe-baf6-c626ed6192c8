﻿package com.cet.eem.fusion.maintenance.core.model.workorder.inspection;

import com.cet.eem.bll.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.Page;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 4/12/2021
 */
@Getter
@Setter
@ApiModel(description = "巡检工单数量统计查询条件")
public class InspectionCountSearchDto {
    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("班组id")
    private Long teamId;

    @ApiModelProperty("工单状态")
    private List<Integer> workSheetStatuses;

    @ApiModelProperty("工单类型")
    private Integer taskType = WorkSheetTaskType.INSPECTION;

    @ApiModelProperty("签到点id")
    private Long signPointId;

    @ApiModelProperty("是否为巡检用户")
    private boolean isInspectUser;
}


