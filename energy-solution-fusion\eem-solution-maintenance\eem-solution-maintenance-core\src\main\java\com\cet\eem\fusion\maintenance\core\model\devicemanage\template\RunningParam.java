﻿package com.cet.eem.fusion.maintenance.core.model.devicemanage.template;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-06
 */
@Data
@AllArgsConstructor
@ApiModel(description = "定时记录参数")

@ModelLabel(ModelLabelDef.RUNNING_PARAM)
public class RunningParam extends EntityWithName {
    /**private String name;*/
    @JsonProperty("dataid")
    private Long dataId;
    /**
     * 测点分组
     */
    private Long measureId;
    public RunningParam() {
        this.modelLabel = ModelLabelDef.RUNNING_PARAM;
    }
}


