﻿package com.cet.eem.fusion.maintenance.core.model.plan;

import com.cet.eem.fusion.common.model.Page;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @ClassName : QueryInspectionPlanRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-21 17:17
 */
@Getter
@Setter
@ApiModel(value = "QueryInspectionPlanRequest", description = "查询巡检计划")
public class QueryInspectionPlanRequest {

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空")
    private Long tenantId;
    /**
     * 巡检计划名称
     */
    private String name;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 隐藏结束方案
     */
    private boolean hide;

    /**
     * 班组id
     */
    private Long teamId;

    /**
     * 分页参数
     */
    private Page page;
}


