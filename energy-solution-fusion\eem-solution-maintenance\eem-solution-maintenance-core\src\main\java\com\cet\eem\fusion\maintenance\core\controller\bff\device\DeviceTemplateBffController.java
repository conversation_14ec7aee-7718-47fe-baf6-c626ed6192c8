﻿package com.cet.eem.fusion.maintenance.core.controller.bff.device;

import com.cet.eem.auth.aspect.OperationPermission;
import com.cet.eem.bll.common.def.OperationAuthDef;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.MeasureNode;
import com.cet.eem.bll.common.model.node.EemNodeFieldInfo;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.template.*;
import com.cet.eem.fusion.maintenance.core.service.device.TemplateService;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.commons.ApiResult;

import com.cet.electric.commons.ApiResult;
import com.cet.eem.toolkit.CollectionUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: fyl
 * @Description:设备模块模板相关接口
 * @Data: Created in 2021-04-29
 */
@Validated
public class DeviceTemplateBffController {

    @Autowired
    private TemplateService templateService;

    @ApiOperation(value = "查看设备模板节点树")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_TEMPLATE_CONFIG_BROWSER})
    @PostMapping(value = "/queryTemplateNodeTree", produces = "application/json")
    public ApiResult<List<EquipmentNodeTreeVo>> queryTemplateNodeTree(
    ) {
        List<EquipmentNodeTreeVo> result = templateService.queryTemplateNodeTree();
        return ApiResult.ok(result);
    }

    @ApiOperation(value = "查看设备模板节点树")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_TEMPLATE_CONFIG_BROWSER})
    @GetMapping(value = "/templateNodeTree", produces = "application/json")
    public ApiResult<List<EquipmentNodeTreeVo>> queryAllTemplateNodeTree() {
        List<EquipmentNodeTreeVo> result = templateService.queryAllTemplateNodeTree();
        return ApiResult.ok(result);
    }

    @ApiOperation(value = "插入设备模板节点树")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_TEMPLATE_CONFIG_UPDATE})
    @RequestMapping(value = "/eem/solution/maintenance/insertTemplateNodeTree", method = RequestMethod.PUT, produces = "application/json")
    public ApiResult<EquipmentNodeTreeDto> insertTemplateNodeTree(
            @RequestBody @ApiParam(value = "节点信息", name = "dataList", required = true) EquipmentNodeTreeDto data
    ) {
        EquipmentNodeTreeDto dto = templateService.insertTemplateNodeTree(data);
        return ApiResult.ok(dto);
    }

    @ApiOperation(value = "更新设备模板节点树")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_TEMPLATE_CONFIG_UPDATE})
    @RequestMapping(value = "/eem/solution/maintenance/editTemplateNodeTree", method = RequestMethod.PUT, produces = "application/json")
    public ApiResult<EquipmentNodeTreeDto> editTemplateNodeTree(
            @RequestBody @ApiParam(value = "节点信息", name = "dataList", required = true) EquipmentNodeTreeDto data
    ) {
        EquipmentNodeTreeDto dto = templateService.editTemplateNodeTree(data);
        return ApiResult.ok(dto);
    }

    @ApiOperation(value = "删除模板节点")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_TEMPLATE_CONFIG_UPDATE})
    @RequestMapping(value = "/eem/solution/maintenance/deleteDeviceTemplateNode", method = RequestMethod.DELETE, produces = "application/json")
    public ApiResult<Object> deleteDeviceTemplateNode(
            @RequestBody @ApiParam(value = "id集合", name = "ids", required = true) List<Long> ids
    ) {
        templateService.deleteNode(ids);
        return ApiResult.ok();
    }

    @ApiOperation(value = "查看模板")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_TEMPLATE_CONFIG_BROWSER})
    @PostMapping(value = "/queryTemplates", produces = "application/json")
    public ApiResult<List<AttributeTemplateVo>> queryTemplates(
            @NotNull @ApiParam(name = "id", value = "节点树id", type = "QueryCondition", required = true) @RequestParam Long id
    ) {
        List<AttributeTemplateVo> result = templateService.queryTemplates(id);
        return ApiResult.ok(result);
    }

    @ApiOperation(value = "查看模板")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_TEMPLATE_CONFIG_BROWSER})
    @PostMapping(value = "/queryTemplates/page", produces = "application/json")
    public ApiResult<List<AttributeTemplateVo>> queryTemplates(@Valid @RequestBody QueryTemplatesSearchVO searchV0) {
        return templateService.queryTemplates(searchV0);
    }

    @ApiOperation(value = "写入模板")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_TEMPLATE_CONFIG_UPDATE})
    @PostMapping(value = "/writeTemplate", produces = "application/json")
    public ApiResult<Object> writeTemplate(
            @NotNull @ApiParam(name = "template", value = "模板对象", type = "AttributeTemplate", required = true) @RequestBody AttributeTemplate template,
            @RequestParam Long parentId
    ) {
        templateService.writeTemplate(template, parentId);
        return ApiResult.ok();
    }

    @ApiOperation(value = "编辑模板")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_TEMPLATE_CONFIG_UPDATE})
    @PostMapping(value = "/updateTemplate", produces = "application/json")
    public ApiResult<Object> updateTemplate(
            @NotNull @ApiParam(name = "template", value = "模板对象", type = "AttributeTemplate", required = true) @RequestBody AttributeTemplate template,
            @RequestParam Long parentId
    ) {
        templateService.updateTemplate(template, parentId);
        return ApiResult.ok();
    }

    @ApiOperation(value = "删除模板")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_TEMPLATE_CONFIG_UPDATE})
    @RequestMapping(value = "/eem/solution/maintenance/deleteDeviceTemplate", method = RequestMethod.DELETE, produces = "application/json")
    public ApiResult<Object> deleteDeviceTemplate(
            @RequestBody @ApiParam(value = "模板id", name = "id", required = true) List<Long> ids,
            @RequestParam Long parentId
    ) {
        if (CollectionUtils.isNotEmpty(ids)) {
            templateService.deleteTemplate(ids.get(0), parentId);
        }
        return ApiResult.ok();
    }

    @ApiOperation(value = "查看运行参数节点树")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_TEMPLATE_CONFIG_BROWSER})
    @PostMapping(value = "/queryRunningParamNodeTree", produces = "application/json")
    public ApiResult<List<RunningParamNodeVo>> queryRunningParamNodeTree(
    ) {
        List<RunningParamNodeVo> result = templateService.queryRunningParamNodeTree();
        return ApiResult.ok(result);
    }

    @ApiOperation(value = "查看运行参数节点")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_TEMPLATE_CONFIG_BROWSER})
    @PostMapping(value = "/queryRunningParamNode", produces = "application/json")
    public ApiResult<List<MeasureNode>> queryRunningParamNode(
            @NotNull @ApiParam(name = "id", value = "节点树id", type = "QueryCondition", required = true) @RequestParam Long id
    ) {
        List<MeasureNode> measureNodes = templateService.queryRunningParam(id);
        return  ApiResult.ok(measureNodes);
    }

    @ApiOperation(value = "查询节点信息带有模板信息")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_TEMPLATE_CONFIG_BROWSER})
    @PostMapping(value = "/nodeByNodeFieldDefWithTemplate")
    public ApiResult<List<EemNodeFieldInfo>> queryNodeInfoByNodeFieldDefWithTemplate(@RequestBody BaseVo node) {
        return ApiResult.ok(templateService.queryNodeInfoByNodeFieldDefWithTemplate(node));
    }
}


