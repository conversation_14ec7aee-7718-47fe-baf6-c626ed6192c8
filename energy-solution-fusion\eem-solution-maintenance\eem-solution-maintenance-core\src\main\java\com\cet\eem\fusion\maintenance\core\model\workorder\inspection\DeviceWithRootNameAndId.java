﻿package com.cet.eem.fusion.maintenance.core.model.workorder.inspection;

import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : DeviceWithRootNameAndId
 * @Description : 配电室-配电柜-一段线三者的id与名称的类
 * <AUTHOR> jiangzixuan
 * @Date: 2021-08-25 16:47
 */
@Getter
@Setter
public class DeviceWithRootNameAndId {
    /**
     * 设备id
     */
    private Long deviceId;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 配电柜id
     */
    private Long powerDisCabinetId;
    /**
     * 配电柜名称
     */
    private String powerDisCabinetName;
    /**
     * 房间id
     */
    private Long roomId;
    /**
     * 房间名称
     */
    private String roomName;

    public DeviceWithRootNameAndId(Long deviceId, String deviceName, Long powerDisCabinetId, String powerDisCabinetName, Long roomId, String roomName) {
        this.deviceId = deviceId;
        this.deviceName = deviceName;
        this.powerDisCabinetId = powerDisCabinetId;
        this.powerDisCabinetName = powerDisCabinetName;
        this.roomId = roomId;
        this.roomName = roomName;
    }
}
