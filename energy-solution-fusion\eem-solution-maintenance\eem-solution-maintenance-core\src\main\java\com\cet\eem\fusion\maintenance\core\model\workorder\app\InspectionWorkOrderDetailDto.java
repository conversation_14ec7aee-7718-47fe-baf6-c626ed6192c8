﻿package com.cet.eem.fusion.maintenance.core.model.workorder.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/20
 */
@Getter
@Setter
@ApiModel("工单详情")
public class InspectionWorkOrderDetailDto {


    @ApiModelProperty("工单数量统计")
    private List<WorkOrderCountDto> workOrderDetails;

    @ApiModelProperty("签到点信息")
    private List<SignPointWithWorkOrder> signPointWithWorkOrders;

    public InspectionWorkOrderDetailDto() {
        //default构造
    }
}

