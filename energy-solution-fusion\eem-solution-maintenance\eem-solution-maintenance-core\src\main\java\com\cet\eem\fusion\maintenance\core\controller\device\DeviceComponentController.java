﻿package com.cet.eem.fusion.maintenance.core.controller.device;

import com.cet.eem.fusion.maintenance.core.controller.bff.device.DeviceComponentBffController;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: jiangzixuan
 * @Description:
 * @Data: Created in 2021-05-12
 */
@Api(value = "/eem/v1/device", tags = "设备管理备件接口")
@RequestMapping(value = "/eem/solution/maintenance/eem/v1/device/spareParts")
@RestController
@Validated
public class DeviceComponentController extends DeviceComponentBffController {

}


