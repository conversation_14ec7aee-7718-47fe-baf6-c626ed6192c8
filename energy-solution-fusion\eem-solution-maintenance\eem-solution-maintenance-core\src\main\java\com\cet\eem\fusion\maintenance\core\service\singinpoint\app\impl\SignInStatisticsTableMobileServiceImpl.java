﻿package com.cet.eem.fusion.maintenance.core.service.singinpoint.app.impl;

import com.cet.eem.auth.service.AuthUtils;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInStatusDef;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.model.sign.SignGroupStatusGroup;
import com.cet.eem.fusion.maintenance.core.model.sign.SignInStatisticsTableWriteVo;
import com.cet.eem.fusion.maintenance.core.service.singinpoint.SignInStatusRecordService;
import com.cet.eem.fusion.maintenance.core.service.singinpoint.app.SignInStatisticsTableMobileService;
import com.cet.eem.common.exception.ValidationException;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/5/12
 */
@Service
public class SignInStatisticsTableMobileServiceImpl implements SignInStatisticsTableMobileService {
    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    AuthUtils authUtils;

    @Autowired
    SignInStatusRecordService signInStatusRecordService;

    @Override
    public void writeSignInStatisticsTable(List<SignInStatisticsTableWriteVo> writeVos) {
        if (CollectionUtils.isEmpty(writeVos)) {
            return;
        }

        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        Long projectId = GlobalInfoUtils.getTenantId();


        for (SignInStatisticsTableWriteVo writeVo : writeVos) {
            Assert.notNull(writeVo.getSignPointId(), "签到点信息不允许为空！");
            Assert.notNull(writeVo.getLogTime(), "签到时间不允许为空！");

            if (writeVo.getSkipSign() != null && writeVo.getSkipSign() && StringUtils.isBlank(writeVo.getDescription())) {
                throw new ValidationException("跳过签到时，必须填写跳过原因！");
            }

            writeVo.setStaffId(user.getId());
            writeVo.setProjectId(projectId);
        }

        modelServiceUtils.writeData(writeVos);

        updateSignInStatus(writeVos);
    }

    /**
     * 更新签到点状态
     *
     * @param writeVos
     */
    private void updateSignInStatus(List<SignInStatisticsTableWriteVo> writeVos) {
        List<SignInStatisticsTableWriteVo> abnormalSingPoints = writeVos.stream().filter(it -> BooleanUtils.isTrue(it.getSkipSign())).collect(Collectors.toList());
        List<SignInStatisticsTableWriteVo> normalSingPoints = writeVos.stream().filter(it -> !BooleanUtils.isTrue(it.getSkipSign())).collect(Collectors.toList());
        List<SignGroupStatusGroup> list = getSignGroupStatusGroups(abnormalSingPoints, SignInStatusDef.ABNORMAL);
        list.addAll(getSignGroupStatusGroups(normalSingPoints, SignInStatusDef.NORMAL));
        signInStatusRecordService.updateSignInStatus(list);
    }

    private List<SignGroupStatusGroup> getSignGroupStatusGroups(List<SignInStatisticsTableWriteVo> abnormalSingPoints, int status) {
        Map<Long, List<Long>> recordGroupMap = new HashMap<>(abnormalSingPoints.size());
        for (SignInStatisticsTableWriteVo abnormalSingPoint : abnormalSingPoints) {
            List<Long> longs = recordGroupMap.computeIfAbsent(abnormalSingPoint.getSignGroupId(), k -> new ArrayList<>());
            longs.add(abnormalSingPoint.getSignPointId());
        }
        List<SignGroupStatusGroup> list = new ArrayList<>();
        recordGroupMap.forEach((key, val) -> {
            SignGroupStatusGroup group = new SignGroupStatusGroup();
            group.setSignGroupId(key);
            group.setSignPointIds(val);
            group.setStatus(status);
        });
        return list;
    }
}


