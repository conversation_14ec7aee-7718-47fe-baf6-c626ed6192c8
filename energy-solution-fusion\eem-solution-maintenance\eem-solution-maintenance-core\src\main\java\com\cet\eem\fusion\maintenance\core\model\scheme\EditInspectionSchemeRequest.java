﻿package com.cet.eem.fusion.maintenance.core.model.scheme;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.InspectionSchemeDetail;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName : EditInspectionSchemeRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-14 17:03
 */
@Getter
@Setter
public class EditInspectionSchemeRequest {

    @NotNull(message = "巡检方案id不能为空")
    private Long id;

    @NotEmpty(message = "巡检方案名称不能为空")
    private String name;

    private List<InspectionSchemeDetail> schemeDetails;

}

