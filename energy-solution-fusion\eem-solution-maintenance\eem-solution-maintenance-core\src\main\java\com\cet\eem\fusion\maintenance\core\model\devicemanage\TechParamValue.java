﻿package com.cet.eem.fusion.maintenance.core.model.devicemanage;/**
 * <AUTHOR>
 * @date 2021/4/27 16:10
 */

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.template.TechParam;
import com.cet.eem.common.definition.ModelLabelDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-04-27
 */
@Data
@AllArgsConstructor
@ModelLabel(ModelLabelDef.TECH_PARAM_DATA)
public class TechParamValue extends TechParam {
    @JsonProperty("objectid")
    private Long objectId;

    @JsonProperty("objectlabel")
    private String objectLabel;

    private Double number;
    @JsonProperty("techparamtemplateid")
    private Long techParamTemplateId;

    private String value;

    public TechParamValue() {
        this.modelLabel = ModelLabelDef.TECH_PARAM_DATA;
    }

    public TechParamValue(String paramName, String paramUnit) {
        super(paramName, paramUnit);
        this.modelLabel = ModelLabelDef.TECH_PARAM_DATA;
    }


    public TechParamValue(String paramName, String paramUnit, Long techParamTemplateId) {
        super(paramName, paramUnit);
        this.techParamTemplateId = techParamTemplateId;
        this.modelLabel = ModelLabelDef.TECH_PARAM_DATA;
    }
}


