﻿package com.cet.eem.fusion.maintenance.core.controller;

import com.cet.eem.fusion.maintenance.core.controller.bff.HandoverBffController;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName : HandoverController
 * @Description :
 * <AUTHOR> zhangzhuang
 * @Date: 2021-03-12 16:49
 */
@Api(value = "/eem/v1/handover", tags = "交接班")
@RequestMapping(value = "/eem/solution/maintenance/eem/v1/handover")
@RestController
@Validated
public class HandoverController extends HandoverBffController {

}


