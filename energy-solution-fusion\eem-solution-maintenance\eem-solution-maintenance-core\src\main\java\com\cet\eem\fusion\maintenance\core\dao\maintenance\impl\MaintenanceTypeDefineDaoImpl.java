﻿package com.cet.eem.fusion.maintenance.core.dao.maintenance.impl;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.MaintenanceTypeDefine;
import com.cet.eem.fusion.maintenance.core.dao.maintenance.MaintenanceTypeDefineDao;
import com.cet.eem.fusion.common.utils.ParamUtils;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/13
 */
@Repository
public class MaintenanceTypeDefineDaoImpl extends ModelDaoImpl<MaintenanceTypeDefine> implements MaintenanceTypeDefineDao {
    @Override
    public List<MaintenanceTypeDefine> queryTypeByNames(Collection<String> names, Long projectId) {
        if(CollectionUtils.isEmpty(names)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaintenanceTypeDefine> wrapper = LambdaQueryWrapper.of(MaintenanceTypeDefine.class)
                .eq(MaintenanceTypeDefine::getProjectId, projectId)
                .in(BaseEntity::getName, names);

        return this.selectList(wrapper);
    }

    @Override
    public List<MaintenanceTypeDefine> queryTypeByName(String key, Long projectId) {
        LambdaQueryWrapper<MaintenanceTypeDefine> wrapper = LambdaQueryWrapper.of(MaintenanceTypeDefine.class);
        if (ParamUtils.checkPrimaryKeyValid(projectId)) {
            wrapper.eq(MaintenanceTypeDefine::getProjectId, projectId);
        }

        if (StringUtils.isNotBlank(key)) {
            wrapper.like(BaseEntity::getName, key);
        }

        return this.selectList(wrapper);
    }

}


