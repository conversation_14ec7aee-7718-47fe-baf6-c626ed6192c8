﻿package com.cet.eem.fusion.maintenance.core.service.inspection;

import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderCheckInfoVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.WorkOrderReviewVo;
import com.cet.eem.fusion.maintenance.core.model.workorder.WorkOrderCountDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.*;
import com.cet.eem.fusion.maintenance.core.schedule.event.CreateOrderCommand;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.workflow.common.model.ProcessInstanceResponse;
import com.cet.electric.workflow.common.model.node.config.UserTaskConfig;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 巡检工单
 *
 * <AUTHOR>
 * @date 4/12/2021
 */
public interface InspectionWorkOrderService {

    ApiResult<List<InspectionParameterWorkOrderDTO>> queryInspectionParameterWorkOrder(InspectionSearchDto dto);

    /**
     * 查询工单列表
     *
     * @param dto
     * @return
     */
    ApiResult<List<InspectionWorkOrderDto>> queryWorkOrderList(InspectionSearchDto dto);

    ApiResult<List<InspectionWorkOrderDto>> queryFinshWorkOrderList(InspectionSearchDto dto);

    /**
     * 统计工单数量
     *
     * @param dto
     * @return
     */
    List<WorkOrderCountDto> queryWorkOrderCount(InspectionCountSearchDto dto);

    /**
     * 新增或者更新工单
     *
     * @param dto
     * @return
     */
    ProcessInstanceResponse createWorkOrder(InspectionAddDto dto);

    /**
     * 根据计划创建工单
     *
     * @param command
     */
    boolean createWorkOrderByPlanSheet(CreateOrderCommand command);

    /**
     * 根据工单id查询工单
     */
    InspectionWorkOrderDto queryWorkOrder(Long workOrderId);

    /**
     * 根据工单编号查询工单
     *
     * @param code
     * @return
     */
    InspectionWorkOrderDto queryWorkOrder(String code);

    /**
     * 查询工单节点配置信息
     *
     * @param code
     * @return
     */
    UserTaskConfig queryTaskConfig(String code);

    /**
     * 组装公共数据
     *
     * @param workOrderList
     * @param tenantId
     */
    void assemblyCommonData(List<InspectionWorkOrderDto> workOrderList, Long tenantId);

    /**
     * 更新工单
     *
     * @param inspectParamsUpdateVo
     */
    void updateWorkOrder(InspectParamsWriteVo inspectParamsUpdateVo);

    /**
     * 提交审批参数
     *
     * @param inspectParamsPointVo
     */
    void submitInspectParams(InspectParamsWriteVo inspectParamsPointVo);

    /**
     * 更新超时状态
     */
    void updateOverTimeStatus();

    /**
     * 通知超时的工单
     */
    void noticeOverTimeStatus();

    /**
     * 巡检工单导出
     *
     * @param response
     */
    void exportWorkOrder(InspectionSearchDto dto, HttpServletResponse response);


    /**
     * 更新签到点状态
     */
    void updateSignPointStatus();
}


