﻿package com.cet.eem.fusion.maintenance.core.model.handover;

import com.cet.eem.fusion.maintenance.core.def.HandoverDef;
import com.cet.eem.fusion.maintenance.core.model.workorder.OperationUser;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 接班
 *
 * <AUTHOR>
 * @date 2021/5/6
 */
@Getter
@Setter
public class SuccessionDto {
    @ApiModelProperty("值班员")
    private List<OperationUser> dutyStaffList;
}

