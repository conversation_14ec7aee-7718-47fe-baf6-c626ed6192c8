﻿package com.cet.eem.fusion.maintenance.core.controller.repair;

import com.cet.eem.fusion.maintenance.core.controller.bff.repair.RepairWorkOrderBffController;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021-04-02 10:08
 */
@Api(value = "/eem/v1/workorder/repair", tags = "工单：维修工单")
@RequestMapping(value = "/eem/solution/maintenance/eem/v1/workorder/repair")
@RestController
@Validated
public class RepairWorkOrderController extends RepairWorkOrderBffController {

}


