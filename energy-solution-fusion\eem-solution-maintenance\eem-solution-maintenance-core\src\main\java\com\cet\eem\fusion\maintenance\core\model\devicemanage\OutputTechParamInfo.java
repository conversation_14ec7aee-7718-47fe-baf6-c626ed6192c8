﻿package com.cet.eem.fusion.maintenance.core.model.devicemanage;

import com.cet.eem.fusion.maintenance.core.model.devicemanage.template.AttributeTemplate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OutputTechParamInfo {

    private Long id;

    private String modelLabel;

    private String name;

    private String code;

    private List<TechParamValue> values;

    private AttributeTemplate template;

    public OutputTechParamInfo(Long id, String modelLabel, String name, String code) {
        this.id = id;
        this.modelLabel = modelLabel;
        this.name = name;
        this.code = code;
    }
}

