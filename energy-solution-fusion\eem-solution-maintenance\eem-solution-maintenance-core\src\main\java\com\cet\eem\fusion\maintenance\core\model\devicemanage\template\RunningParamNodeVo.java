﻿package com.cet.eem.fusion.maintenance.core.model.devicemanage.template;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.RunningParamNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RunningParamNodeVo extends RunningParamNode {

    private List<RunningParamNodeVo> children;

    public RunningParamNodeVo(String nodeName, Long parentId, List<RunningParamNodeVo> children) {
        super(nodeName, parentId);
        this.children = children;
    }

    public RunningParamNodeVo(String nodeName, Long parentId) {
        super(nodeName, parentId);
    }
}

