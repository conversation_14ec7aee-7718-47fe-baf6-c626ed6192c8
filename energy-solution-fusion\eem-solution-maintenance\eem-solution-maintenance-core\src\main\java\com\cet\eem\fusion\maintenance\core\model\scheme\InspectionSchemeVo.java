﻿package com.cet.eem.fusion.maintenance.core.model.scheme;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.InspectionScheme;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

/**
 * @ClassName : InspectorSchemeVo
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-14 16:06
 */
@Getter
@Setter
public class InspectionSchemeVo extends InspectionScheme {

    /**
     * 用户名
     */
    private String userName;

    public InspectionSchemeVo(InspectionScheme inspectionScheme) {
        BeanUtils.copyProperties(inspectionScheme, this);
    }
}

