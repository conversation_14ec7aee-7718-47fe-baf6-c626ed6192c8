﻿package com.cet.eem.fusion.maintenance.core.controller.bff;

import com.cet.eem.fusion.maintenance.core.model.handover.*;
import com.cet.eem.fusion.maintenance.core.service.hanover.HandoverService;
import com.cet.electric.commons.ApiResult;

import com.cet.electric.commons.ApiResult;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 文件管理
 *
 * <AUTHOR>
 * @date 2020/1/17 9:32
 */
public class HandoverBffController {

    @Autowired
    HandoverService handoverService;

    @ApiOperation(value = "交接班列表")
    @PostMapping(value = "/list")
    public ApiResult<List<ShiftingDutyVo>> getHandoverLst(@NotNull @RequestBody ShiftingDutySearchDto dto) {
        return handoverService.getHandoverList(dto);
    }

    @ApiOperation(value = "交接班详情")
    @PostMapping(value = "/handoverInfo")
    public ApiResult<ShiftingDutyVo> getHandoverDetailInfo(
            @RequestParam @ApiParam(name = "id", value = "记录id", required = true) Long id,
            @RequestParam @ApiParam(name = "tenantId", value = "租户id", required = true) Long tenantId) {
        ShiftingDutyVo result = handoverService.getHandoverDetailInfo(id, tenantId);
        return ApiResult.ok(result);
    }

    @ApiOperation(value = "接班")
    @PostMapping(value = "/succession")
    public ApiResult<ShiftingDutyPo> succession(
            @NotNull @RequestBody SuccessionDto handoverDo) {
        ShiftingDutyPo result = handoverService.succession(handoverDo);
        return ApiResult.ok(result);
    }

    @ApiOperation(value = "交班")
    @PostMapping(value = "/handOverToNext")
    public ApiResult<ShiftingDutyPo> handOverToNext(@NotNull @RequestBody ShiftingDutyUpdateVo shiftingDutyUpdateVo) {
        ShiftingDutyPo result = handoverService.handOverToNext(shiftingDutyUpdateVo);
        return ApiResult.ok(result);
    }

    @ApiOperation(value = "获取接班信息")
    @GetMapping(value = "/handoverMatter")
    public ApiResult<HandoverMatterVo> getHandovermMatter(
            @RequestParam @ApiParam(name = "tenantId", value = "租户id", required = true) Long tenantId) {
        HandoverMatterVo handoverMatter = handoverService.getHandoverMatter(tenantId);
        return ApiResult.ok(handoverMatter);
    }

    @ApiOperation(value = "获取当前值班信息")
    @GetMapping(value = "/onDutyRecord")
    public ApiResult<ShiftingDutyVo> queryOnDutyRecord(
            @RequestParam @ApiParam(name = "tenantId", value = "租户id", required = true) Long tenantId) {
        ShiftingDutyVo handoverMatter = handoverService.queryOnDutyRecord(tenantId);
        return ApiResult.ok(handoverMatter);
    }

    @ApiOperation(value = "获取当前人员值班状态")
    @GetMapping(value = "/getUserWorkingStatus")
    public ApiResult<String> getUserWorkingStatus() throws Exception {
        String status = handoverService.getUserWorkingStatus();
        return ApiResult.ok(status);
    }

}


