﻿package com.cet.eem.fusion.maintenance.core.model.handover;

import com.cet.eem.fusion.maintenance.core.def.HandoverDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 交班
 *
 * <AUTHOR>
 * @date 2021/5/6
 */
@Getter
@Setter
public class HandOverDto {
    @ApiModelProperty("值班日志")
    @JsonProperty(value = HandoverDef.DUTYLOG)
    private String dutyLog;

    @ApiModelProperty("值班事项")
    @JsonProperty(value = HandoverDef.HANDOVERMATTER)
    private String handoverMatter;
}

