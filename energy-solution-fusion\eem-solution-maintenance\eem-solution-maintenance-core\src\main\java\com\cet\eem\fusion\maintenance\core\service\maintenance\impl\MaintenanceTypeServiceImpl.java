﻿package com.cet.eem.fusion.maintenance.core.service.maintenance.impl;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.MaintenanceItem;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.MaintenanceTypeDefine;
import com.cet.eem.bll.common.util.DataValidationUtils;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.dao.maintenance.MaintenanceItemDao;
import com.cet.eem.fusion.maintenance.core.dao.maintenance.MaintenanceTypeDefineDao;
import com.cet.eem.fusion.maintenance.core.model.maintance.item.AddMaintenanceTypeVo;
import com.cet.eem.fusion.maintenance.core.model.maintance.item.UpdateMaintenanceTypeVo;
import com.cet.eem.fusion.maintenance.core.service.maintenance.MaintenanceTypeService;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.exception.ValidationException;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/13
 */
@Service
public class MaintenanceTypeServiceImpl implements MaintenanceTypeService {
    public static final String REPEAT_MAINTENANCE_TYPE_MSG = "维保类型名称重复，请更换！";
    private final MaintenanceTypeDefineDao maintenanceTypeDefineDao;
    private final MaintenanceItemDao maintenanceItemDao;
    private final ModelServiceUtils modelServiceUtils;

    public MaintenanceTypeServiceImpl(MaintenanceTypeDefineDao maintenanceTypeDefineDao,
                                      MaintenanceItemDao maintenanceItemDao,
                                      ModelServiceUtils modelServiceUtils) {
        this.maintenanceTypeDefineDao = maintenanceTypeDefineDao;
        this.maintenanceItemDao = maintenanceItemDao;
        this.modelServiceUtils = modelServiceUtils;
    }

    @Override
    public List<MaintenanceTypeDefine> queryMaintenanceType(String key) {
        return maintenanceTypeDefineDao.queryTypeByName(key, GlobalInfoUtils.getTenantId());
    }

    @Override
    public List<MaintenanceTypeDefine> createMaintenanceType(List<AddMaintenanceTypeVo> maintenanceTypeVos) {
        if (CollectionUtils.isEmpty(maintenanceTypeVos)) {
            return Collections.emptyList();
        }

        Long projectId = GlobalInfoUtils.getTenantId();
        Assert.notNull(projectId, "项目id不允许为空！");
        Set<String> names = maintenanceTypeVos.stream().map(AddMaintenanceTypeVo::getName).collect(Collectors.toSet());
        if (names.size() != maintenanceTypeVos.size()) {
            throw new ValidationException(REPEAT_MAINTENANCE_TYPE_MSG);
        }

        List<MaintenanceTypeDefine> maintenanceTypeDefines = maintenanceTypeDefineDao.queryTypeByNames(names, projectId);
        if (CollectionUtils.isNotEmpty(maintenanceTypeDefines)) {
            throw new ValidationException(REPEAT_MAINTENANCE_TYPE_MSG);
        }

        List<MaintenanceTypeDefine> typeDefines = new ArrayList<>();
        for (AddMaintenanceTypeVo maintenanceTypeVo : maintenanceTypeVos) {
            typeDefines.add(new MaintenanceTypeDefine(projectId, maintenanceTypeVo.getName()));
        }

        return modelServiceUtils.writeData(typeDefines, MaintenanceTypeDefine.class);
    }
    private void checkParam(List<UpdateMaintenanceTypeVo> maintenanceTypeVos){
        Set<String> nameList = maintenanceTypeVos.stream().map(UpdateMaintenanceTypeVo::getName).collect(Collectors.toSet());
        if (!Objects.equals(nameList.size(),maintenanceTypeVos.size())){
            throw new ValidationException(REPEAT_MAINTENANCE_TYPE_MSG);
        }
    }
    @Override
    public List<MaintenanceTypeDefine> updateMaintenanceType(List<UpdateMaintenanceTypeVo> maintenanceTypeVos) {
        if (CollectionUtils.isEmpty(maintenanceTypeVos)) {
            return Collections.emptyList();
        }

        Long projectId = GlobalInfoUtils.getTenantId();
        Assert.notNull(projectId, "项目id不允许为空！");
        checkParam(maintenanceTypeVos);
        List<MaintenanceTypeDefine> typeDefines = maintenanceTypeDefineDao.selectBatchIds(maintenanceTypeVos.stream().map(UpdateMaintenanceTypeVo::getId).collect(Collectors.toSet()));
        for (UpdateMaintenanceTypeVo maintenanceTypeVo : maintenanceTypeVos) {
            Optional<MaintenanceTypeDefine> any = typeDefines.stream().filter(it -> it.getId().equals(maintenanceTypeVo.getId())).findAny();
            any.ifPresent(maintenanceTypeDefine -> maintenanceTypeDefine.setName(maintenanceTypeVo.getName()));
        }

        Set<String> names = maintenanceTypeVos.stream().map(UpdateMaintenanceTypeVo::getName).collect(Collectors.toSet());
        List<MaintenanceTypeDefine> oldMaintenanceTypeDefines = maintenanceTypeDefineDao.queryTypeByNames(names, projectId);
        if (CollectionUtils.isNotEmpty(oldMaintenanceTypeDefines) ) {
            for (MaintenanceTypeDefine define:typeDefines){
                MaintenanceTypeDefine maintenanceTypeDefine1 = oldMaintenanceTypeDefines.stream()
                        .filter(maintenanceTypeDefine -> !Objects.equals(maintenanceTypeDefine.getId(), define.getId())
                        && Objects.equals(maintenanceTypeDefine.getName(),define.getName()))
                        .findAny().orElse(new MaintenanceTypeDefine());
                if (Objects.nonNull(maintenanceTypeDefine1.getId())){
                    throw new ValidationException(REPEAT_MAINTENANCE_TYPE_MSG);
                }
            }

        }

        DataValidationUtils.checkNameRepeat(typeDefines, oldMaintenanceTypeDefines);
        return modelServiceUtils.writeData(typeDefines, MaintenanceTypeDefine.class);
    }

    @Override
    public void deleteMaintenanceType(List<Long> ids) {
        LambdaQueryWrapper<MaintenanceItem> wrapper = LambdaQueryWrapper.of(MaintenanceItem.class)
                .in(MaintenanceItem::getMaintenanceType, ids);

        ApiResult<List<MaintenanceItem>> result = maintenanceItemDao.selectPage(wrapper, new Page(0, 1));
        if (CollectionUtils.isNotEmpty(result.getData())) {
            throw new ValidationException("维保类型已经被使用，不允许删除！");
        }
        modelServiceUtils.delete(ModelLabelDef.MAINTENANCE_TYPE_DEFINE, ids);
    }


}


