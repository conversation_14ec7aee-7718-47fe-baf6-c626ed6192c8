﻿package com.cet.eem.fusion.maintenance.core.dao.devicemanager.impl;

import com.cet.eem.fusion.maintenance.core.dao.devicemanager.TemplateGroupDao;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.template.GroupWithRunningParam;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.template.RunningParam;
import com.cet.eem.fusion.maintenance.core.model.devicemanage.template.TemplateGroupDto;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-12
 */
@Service
public class TemplateGroupDaoImpl extends ModelDaoImpl<TemplateGroupDto> implements TemplateGroupDao {
    @Override
    public GroupWithRunningParam getRunningParam(Long groupId) {
        LambdaQueryWrapper<RunningParam> queryWrapper = LambdaQueryWrapper.of(RunningParam.class);
        return this.selectRelatedById(GroupWithRunningParam.class, groupId, Collections.singletonList(queryWrapper));
    }
}


