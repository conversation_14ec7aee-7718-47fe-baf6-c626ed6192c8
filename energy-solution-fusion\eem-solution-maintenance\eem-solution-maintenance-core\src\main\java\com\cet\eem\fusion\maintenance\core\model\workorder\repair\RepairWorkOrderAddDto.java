﻿package com.cet.eem.fusion.maintenance.core.model.workorder.repair;

import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.Attachment;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 4/13/2021
 */
@Getter
@Setter
@ApiModel(description = "维修工单新增模型")
public class RepairWorkOrderAddDto {
    @ApiModelProperty("维修目标id")
    @JsonProperty(ColumnDef.C_OBJECT_ID)
    @NotNull(message = "维修目标id不允许为空！")
    private Long objectId;

    @ApiModelProperty("维修目标label")
    @JsonProperty(ColumnDef.C_OBJECT_Label)
    @NotNull(message = "维修目标label不允许为空！")
    private String objectLabel;

    @ApiModelProperty("工单来源，取自枚举workordersourcetype")
    @JsonProperty(WorkOrderDef.SOURCE_TYPE)
    private Integer sourceType;

    @ApiModelProperty("工单来源id")
    @JsonProperty(WorkOrderDef.SOURCE_ID)
    private Long sourceId;

    @ApiModelProperty("来源事件的时间")
    @JsonProperty(WorkOrderDef.SOURCE_TIME)
    private Long sourceTime;

    @ApiModelProperty("工单来源模型")
    @JsonProperty(WorkOrderDef.SOURCE_MODEL)
    private String sourceModel;

    @ApiModelProperty("等级")
    @JsonProperty(WorkOrderDef.TASK_LEVEL)
    @NotNull(message = "等级不允许为空！")
    private Integer taskLevel;

    @ApiModelProperty("预计耗时")
    @JsonProperty(WorkOrderDef.TIME_CONSUME_PLAN)
    @NotNull(message = "预计耗时不允许为空！")
    private Long timeConsumePlan;

    @ApiModelProperty("班组")
    @JsonProperty(WorkOrderDef.TEAM_ID)
    @NotNull(message = "班组信息不允许为空！")
    private Long teamId;

    @ApiModelProperty("故障描述/详情")
    @JsonProperty(WorkOrderDef.FAULT_DESCRIPTION)
    private String faultDescription;

    @ApiModelProperty("设备归类")
    @JsonProperty(ColumnDef.DEVICE_CLASSIFICATION_ID)
    private Long deviceClassificationId;

    @ApiModelProperty("事件归类")
    @JsonProperty(ColumnDef.EVENT_CLASSIFICATION_ID)
    private Long eventClassificationId;

    @ApiModelProperty("故障场景")
    @JsonProperty(ColumnDef.FAULT_SCENARIOS_ID)
    private Long faultScenariosId;

    @ApiModelProperty("项目id")
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;

    @ApiModelProperty("工单状态")
    @JsonProperty(WorkOrderDef.WORKSHEET_STATUS)
    @NotNull(message = "工单状态不允许为空！")
    private Integer workSheetStatus;

    @ApiModelProperty("附件列表")
    private List<Attachment> attachmentList;

    @ApiModelProperty("巡检班组id")
    @JsonProperty(WorkOrderDef.INSPECT_TEAM_ID)
    private Long inspectTeamId;

//    @ApiModelProperty("工单来源模型的索引字段")
//    @JsonProperty(WorkOrderDef.SOURCE_INDEX)
//    private String sourceIndex;
    /**
     * peccore设备的id
     */
    @JsonProperty(ColumnDef.DEVICE_ID)
    private Long deviceId;
    /**
     * 事件发生的时间
     */
    @JsonProperty(ColumnDef.EVENT_TIME)
    private Long eventTime;
    /**
     * 事件类型
     */
    @JsonProperty(ColumnDef.PEC_EVENT_TYPE)
    private Integer pecEventType;


    private Integer code1;

    private Integer code2;

    @JsonProperty(ColumnDef.EVENT_BYTE)
    private Integer eventByte;

    @ApiModelProperty("维修类型")
    @JsonProperty(WorkOrderDef.REPAIR_CATEGORY)
    private Integer repairCategory;
    @ApiModelProperty("工单类别-维修专业类别")
    @JsonProperty(WorkOrderDef.PROFESSIONAL_CATEGORY)
    private Integer professionalCategory;
}


