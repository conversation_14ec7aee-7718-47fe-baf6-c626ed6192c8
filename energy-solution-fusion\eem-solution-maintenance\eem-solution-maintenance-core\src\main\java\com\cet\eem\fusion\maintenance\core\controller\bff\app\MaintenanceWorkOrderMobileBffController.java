﻿package com.cet.eem.fusion.maintenance.core.controller.bff.app;

import com.cet.eem.auth.aspect.OperationPermission;
import com.cet.eem.bll.common.def.OperationAuthDef;
import com.cet.eem.fusion.maintenance.core.model.workorder.app.MaintenanceWorkOrderCountDto;
import com.cet.eem.fusion.maintenance.core.service.maintenance.app.MaintenanceWorkOrderMobileBffService;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @ClassName : MaintenanceWorkOrderBffMobileController
 * @Description : 查询维保工单
 * <AUTHOR> jiangzixuan
 * @Date: 2021-06-08 10:06
 */
public class MaintenanceWorkOrderMobileBffController {
    @Autowired
    private MaintenanceWorkOrderMobileBffService maintenanceWorkOrderMobileBffService;

    @ApiOperation(value = "查询维保工单")
    @PostMapping("/maintenanceWorkOrder")
    @OperationPermission(authNames = {OperationAuthDef.MAINTENANCE_WORK_ORDER_BROWSER})
    public ApiResult<List<MaintenanceWorkOrderCountDto>> queryWorkOrderList(
            @RequestBody(required = false) @ApiParam(name = "page", value = "分页信息") Page page) {
        List<MaintenanceWorkOrderCountDto> workOrderCountDtos= maintenanceWorkOrderMobileBffService.queryWorkOrder(page);
        return ApiResult.ok(workOrderCountDtos);
    }

}


