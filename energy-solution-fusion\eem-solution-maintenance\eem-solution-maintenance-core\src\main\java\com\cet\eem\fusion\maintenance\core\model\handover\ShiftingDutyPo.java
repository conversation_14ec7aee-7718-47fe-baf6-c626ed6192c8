﻿package com.cet.eem.fusion.maintenance.core.model.handover;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.maintenance.core.def.HandoverDef;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 交接班
 *
 * <AUTHOR>
 * @date 2020年6月22日 14:22:10
 */
@Getter
@Setter
@ApiModel(description = "交接班")
@ModelLabel(ModelLabelDef.SHIFTING_DUTY)
public class ShiftingDutyPo extends EntityWithName {
    @ApiModelProperty("值班员")
    @JsonProperty(value = HandoverDef.DUTYSTAFF)
    private String dutyStaff;

    @ApiModelProperty("值班日志")
    @JsonProperty(value = HandoverDef.DUTYLOG)
    private String dutyLog;

    @ApiModelProperty("值班事项")
    @JsonProperty(value = HandoverDef.HANDOVERMATTER)
    private String handoverMatter;

    @ApiModelProperty("开始时间")
    @JsonProperty(value = HandoverDef.STARTTIME)
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    @JsonProperty(value = HandoverDef.ENDTIME)
    private LocalDateTime endTime;

    @ApiModelProperty("值班时长，时间戳")
    @JsonProperty(value = HandoverDef.DUTYTIME)
    private Long dutyTime;

    @ApiModelProperty("值班长")
    @JsonProperty(value = HandoverDef.DUTYOFFICER)
    private Long dutyOfficer;

    @ApiModelProperty("值班状态，取值自模型dutystatus")
    @JsonProperty(value = HandoverDef.DUTYSTATUS)
    private Integer dutyStatus;

    @ApiModelProperty("接班员")
    @JsonProperty(value = HandoverDef.HANDOVERSTAFF)
    private Long handoverStaff;

    @ApiModelProperty("交班员")
    @JsonProperty(value = HandoverDef.HANDOVERFROMSTAFF)
    private Long handoverFromStaff;

    @ApiModelProperty("交班注意事项")
    @JsonProperty(value = HandoverDef.LASTHANDOVERMATTER)
    private String lastHandoverMatter;

    @ApiModelProperty("班组id")
    @JsonProperty(value = HandoverDef.TEAM_ID)
    private Long teamId;

    @ApiModelProperty("交接确认")
    @JsonProperty(value = HandoverDef.DELIVERY_CONFIRMATION)
    private Boolean deliveryConfirmation;

    @ApiModelProperty("项目id")
    @JsonProperty(value = ColumnDef.PROJECT_ID)
    private Long projectId;

    public ShiftingDutyPo() {
        this.modelLabel = ModelLabelDef.SHIFTING_DUTY;
    }
}


