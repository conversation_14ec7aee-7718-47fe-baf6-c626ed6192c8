﻿package com.cet.eem.fusion.maintenance.core.model.workorder.inspection;

import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.Attachment;
import com.cet.eem.fusion.maintenance.core.model.workorder.MaintenanceContent;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 4/13/2021
 */
@Getter
@Setter
@ApiModel(description = "巡检工单修改模型")
public class InspectionUpdateDto {
    @ApiModelProperty("记录主id")
    private Long id;

    @ApiModelProperty("巡检方案id")
    private Long inspectionSchemeId;

    @ApiModelProperty("计划执行时间")
    private LocalDateTime executeTimePlan;

    @ApiModelProperty("班组")
    private Long teamId;

    @ApiModelProperty("预计耗时")
    private Long timeConsumePlan;

    @ApiModelProperty("巡检目标id")
    private Long objectId;

    @ApiModelProperty("巡检目标label")
    private String objectLabel;

    /**
     * 工单状态
     */
    @Range(min = 1, max = 3)
    private Integer workSheetStatus;

    @ApiModelProperty("巡检参数")
    @JsonProperty(WorkOrderDef.MAINTENANCE_CONTENT)
    private MaintenanceContent maintenanceContent;

    @ApiModelProperty("故障及处理描述")
    @JsonProperty(WorkOrderDef.FAULT_DESCRIPTION)
    private String faultDescription;

    @ApiModelProperty("工单附件")
    private Attachment attachment;
}

