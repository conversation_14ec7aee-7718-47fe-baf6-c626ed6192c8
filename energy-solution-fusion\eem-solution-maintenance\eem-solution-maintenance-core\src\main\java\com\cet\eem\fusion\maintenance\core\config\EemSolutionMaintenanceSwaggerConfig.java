package com.cet.eem.fusion.maintenance.core.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

@Configuration
@EnableSwagger2
public class EemSolutionMaintenanceSwaggerConfig {
    @Bean
    public Docket docket() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(getApiInfo())
                .groupName("eem solution 运维插件")
                .enable(true)
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.cet.eem.fusion.maintenance.core.controller"))
                .paths(PathSelectors.any())
                .build()
                .globalOperationParameters(defaultHeader());
    }

    private ApiInfo getApiInfo() {
        return new ApiInfoBuilder()
                .title("eem solution 运维插件接口文档")
                .description("eem solution 运维插件接口")
                .version("1.0")
                .build();
    }
    
    private List<Parameter> defaultHeader() {
        return new ArrayList<>();
    }
}
