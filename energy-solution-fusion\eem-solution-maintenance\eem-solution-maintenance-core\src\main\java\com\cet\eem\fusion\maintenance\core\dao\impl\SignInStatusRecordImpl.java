﻿package com.cet.eem.fusion.maintenance.core.dao.impl;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInStatusRecord;
import com.cet.eem.fusion.maintenance.core.dao.SignInStatusRecordDao;
import com.cet.eem.fusion.maintenance.core.model.sign.SignGroupStatusGroup;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName : SignInGroupDaoImpl
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-03-12 14:09
 */
@Repository
public class SignInStatusRecordImpl extends ModelDaoImpl<SignInStatusRecord> implements SignInStatusRecordDao {
    @Override
    public SignInStatusRecord queryRecord(Long signPointId, Long signGroupId) {
        LambdaQueryWrapper<SignInStatusRecord> wrapper = LambdaQueryWrapper.of(SignInStatusRecord.class)
                .eq(SignInStatusRecord::getSignInGroupId, signGroupId)
                .eq(SignInStatusRecord::getSignInPointId, signPointId);

        List<SignInStatusRecord> signInStatusRecords = this.selectList(wrapper);
        if (CollectionUtils.isEmpty(signInStatusRecords)) {
            return null;
        }

        return signInStatusRecords.get(0);
    }

    @Override
    public List<SignInStatusRecord> queryRecord(Collection<Integer> signPointStatus, Collection<Long> signPointIds, Long singGroupId) {
        LambdaQueryWrapper<SignInStatusRecord> wrapper = LambdaQueryWrapper.of(SignInStatusRecord.class)
                .in(SignInStatusRecord::getStatus, signPointStatus)
                .eq(SignInStatusRecord::getSignInGroupId, singGroupId)
                .in(SignInStatusRecord::getSignInPointId, signPointIds);

        return this.selectList(wrapper);
    }

    @Override
    public List<SignInStatusRecord> queryRecord(Long signPointId, Collection<Long> singGroupIds) {
        LambdaQueryWrapper<SignInStatusRecord> wrapper = LambdaQueryWrapper.of(SignInStatusRecord.class)
                .in(SignInStatusRecord::getSignInGroupId, singGroupIds)
                .eq(SignInStatusRecord::getSignInPointId, signPointId);

        return this.selectList(wrapper);
    }

    @Override
    public List<SignInStatusRecord> queryRecord(List<SignGroupStatusGroup> groups) {
        if (CollectionUtils.isEmpty(groups)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<SignInStatusRecord> wrapper = LambdaQueryWrapper.of(SignInStatusRecord.class);
        for (SignGroupStatusGroup group : groups) {
            wrapper.or(it -> it.eq(SignInStatusRecord::getSignInGroupId, group.getSignGroupId())
                    .in(SignInStatusRecord::getSignInPointId, group.getSignPointIds()));
        }

        return this.selectList(wrapper);
    }
}


