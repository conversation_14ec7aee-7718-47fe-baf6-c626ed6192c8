﻿package com.cet.eem.fusion.maintenance.core.service.maintenance.app.impl;

import com.cet.eem.auth.service.AuthUtils;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SpareParts;
import com.cet.eem.bll.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.maintenance.core.dao.WorkOrderDao;
import com.cet.eem.fusion.maintenance.core.dao.devicecomponent.SparePartsDao;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.fusion.maintenance.core.def.WorkSheetStatusDef;
import com.cet.eem.fusion.maintenance.core.model.workorder.MaintenanceContent;
import com.cet.eem.fusion.maintenance.core.model.workorder.app.AddMaintenanceWorkOrder;
import com.cet.eem.fusion.maintenance.core.model.workorder.app.MaintenanceWorkOrderCountDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.maintenance.MaintenanceWorkOrderDto;
import com.cet.eem.fusion.maintenance.core.model.workorder.maintenance.SparePartNumber;
import com.cet.eem.fusion.maintenance.core.service.WorkOrderService;
import com.cet.eem.fusion.maintenance.core.service.maintenance.MaintenanceWorkOrderService;
import com.cet.eem.fusion.maintenance.core.service.maintenance.app.MaintenanceWorkOrderMobileBffService;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.electric.workflow.api.UserTaskRestApi;
import com.cet.electric.workflow.common.model.params.UserTaskParams;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : MaintenanceWorkOrderMobileBffServiceImpl
 * @Description : 维保工单app的serviceimpl
 * <AUTHOR> jiangzixuan
 * @Date: 2021-06-08 10:10
 */
@Service
public class MaintenanceWorkOrderMobileBffServiceImpl implements MaintenanceWorkOrderMobileBffService {

    @Autowired
    AuthUtils authUtils;
    @Autowired
    SparePartsDao sparePartsDao;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    WorkOrderDao workOrderDao;
    @Autowired
    MaintenanceWorkOrderService maintenanceWorkOrderService;
    @Autowired
    WorkOrderService workOrderService;
    @Autowired
    private UserTaskRestApi workflowService;
    @Value("${cet.eem.work-order.repair.app.summary-query-pre-day:1}")
    private Integer preDay;


    @Override
    public List<MaintenanceWorkOrderCountDto> queryWorkOrder(Page page) {
        // 查询用户信息，获取用户所在的班组信息
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        Long teamId = null;
        List<Long> groupIds = user.getRelativeUserGroup();
        if (CollectionUtils.isNotEmpty(groupIds)) {
            teamId = groupIds.get(0);
        }
        // 根据班组查询工单信息
        List<MaintenanceWorkOrderDto> workOrderList = queryWorkOrder(user, teamId);
        // 根据待处理的工单关联的对象去统计签到点的数量和巡检设备进度数量
        List<MaintenanceWorkOrderCountDto> result = countWorkOrderByStatus(workOrderList, page);

        List<MaintenanceWorkOrderDto> list = new ArrayList<>();
        result.forEach(it -> {
            if (CollectionUtils.isNotEmpty(it.getWorkOrders())) {
                list.addAll(it.getWorkOrders());
            }
        });

        maintenanceWorkOrderService.assemblyCommonData(list, user.getTenantId());
        return result;
    }

    @Override
    public void submitInputWorkOrder(AddMaintenanceWorkOrder addMaintenanceWorkOrder) {
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        InspectionWorkOrderDto workOrder = workOrderService.queryRuntimeWorkOrder(addMaintenanceWorkOrder.getCode());
        UserTaskParams userTaskParams = new UserTaskParams();
        Map<String, Object> params = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        params.put(WorkOrderDef.ID, addMaintenanceWorkOrder.getId());
        params.put(ColumnDef.MODEL_LABEL, ModelLabelDef.PM_WORK_SHEET);
        params.put(WorkOrderDef.ATTACHMENT, JsonTransferUtils.toJSONString(addMaintenanceWorkOrder.getAttachment()));
        params.put(WorkOrderDef.FINISH_TIME, addMaintenanceWorkOrder.getFinishTime());
        MaintenanceContent maintenanceWorkOrderContent = new MaintenanceContent();
        handleMaintenanceWorkOrderContent(addMaintenanceWorkOrder);
        maintenanceWorkOrderContent.setSparePartNumbers(addMaintenanceWorkOrder.getSparePartNumbers());
        maintenanceWorkOrderContent.setItemExtends(addMaintenanceWorkOrder.getItemExtends());
        params.put(WorkOrderDef.MAINTENANCE_CONTENT, JsonTransferUtils.toJSONString(maintenanceWorkOrderContent));
        params.put(WorkOrderDef.HANDLE_DESCRIPTION, addMaintenanceWorkOrder.getDescription());
        params.put(WorkOrderDef.STAFFNAME, addMaintenanceWorkOrder.getStaffName());
        userTaskParams.setFormData(params);
        workOrderDao.submitForm(user.getId(), workOrder.getTaskId(), userTaskParams);
    }

    private void handleMaintenanceWorkOrderContent(AddMaintenanceWorkOrder addMaintenanceWorkOrder) {
        List<Long> collect = addMaintenanceWorkOrder.getSparePartNumbers().stream().map(SparePartNumber::getId).collect(Collectors.toList());
        List<SpareParts> sparePartsList = sparePartsDao.selectBatchIds(collect);
        for (SparePartNumber item : addMaintenanceWorkOrder.getSparePartNumbers()) {
            for (SpareParts itemNow : sparePartsList) {
                if (item.getId().equals(itemNow.getId())) {
                    item.setName(itemNow.getName());
                }
            }
        }
    }

    /**
     * 查询工单
     *
     * @param user
     * @param teamId
     * @return
     */
    private List<MaintenanceWorkOrderDto> queryWorkOrder(UserVo user, Long teamId) {
        // 待处理、待审核和退回的一直显示。超时和已完成的，默认显示一天或者一周，此处取24（或者一周，此处做成配置项）内

        // 查询超时和已完成
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime st = now.plusDays(-1L * preDay);
        // 查询提前指定时间的工单信息
        List<Integer> statuses = Arrays.asList(WorkSheetStatusDef.ACCOMPLISHED);
        ApiResult<List<Map<String, Object>>> workOrderResult = workOrderDao.queryWorkOrderByWorkStatus(st, now, WorkSheetTaskType.MAINTENANCE, statuses, teamId, user.getId());
        List<InspectionWorkOrderDto> workOrderList = JsonTransferUtils.transferList(workOrderResult.getData(), InspectionWorkOrderDto.class);

        // 查询待处理、待审核和退回工单
        statuses = Arrays.asList(WorkSheetStatusDef.TO_BE_AUDITED, WorkSheetStatusDef.TO_BE_SENT, WorkSheetStatusDef.AUDITED);
        LocalDateTime et = TimeUtil.getFirstTimeOfNextDay(now.toLocalDate());
        workOrderList.addAll(workOrderDao.queryRuntimeWorkOrderByWorkStatus(statuses, WorkSheetTaskType.MAINTENANCE, teamId, et, user.getId(), null, InspectionWorkOrderDto.class));
        List<MaintenanceWorkOrderDto> maintenanceWorkOrderDtos = new ArrayList<>();
        for (InspectionWorkOrderDto inspectionWorkOrderDto : workOrderList) {
            MaintenanceWorkOrderDto maintenanceWorkOrderDto = new MaintenanceWorkOrderDto();
            BeanUtils.copyProperties(inspectionWorkOrderDto, maintenanceWorkOrderDto);
            maintenanceWorkOrderDtos.add(maintenanceWorkOrderDto);
        }
        return maintenanceWorkOrderDtos;
    }


    /**
     * 根据工单状态统计工单数量
     *
     * @param workOrderList
     * @return
     */
    private List<MaintenanceWorkOrderCountDto> countWorkOrderByStatus(List<MaintenanceWorkOrderDto> workOrderList, Page page) {
        if (page == null) {
            page = new Page(0, Integer.MAX_VALUE);
        }
        List<MaintenanceWorkOrderCountDto> workOrderDetails = new ArrayList<>();
        Map<Integer, String> maintenanceIdNameMap = WorkSheetStatusDef.MAINTENANCE_ID_NAME_MAP;
        Map<Integer, List<MaintenanceWorkOrderDto>> workStatusMap = workOrderList.stream().collect(Collectors.groupingBy(MaintenanceWorkOrderDto::getWorkSheetStatus));
        Page finalPage = page;
        workStatusMap.forEach((key, val) -> {
            MaintenanceWorkOrderCountDto workOrderCountDto = new MaintenanceWorkOrderCountDto();
            workOrderCountDto.setWorkOrderStatus(key);
            workOrderCountDto.setWorkOrderStatusName(maintenanceIdNameMap.get(key));
            workOrderCountDto.setCount(val.size());
            workOrderCountDto.setWorkOrders(val.stream()
                    .sorted((v1, v2) -> CommonUtils.sort(v1.getExecuteTimePlan(), v2.getExecuteTimePlan(), true))
                    .skip(finalPage.getIndex()).limit(finalPage.getLimit()).collect(Collectors.toList()));
            workOrderDetails.add(workOrderCountDto);
        });
        return workOrderDetails;
    }


}

