﻿package com.cet.eem.fusion.maintenance.core.model.workorder.inspection;

import com.cet.eem.bll.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.fusion.common.model.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 4/12/2021
 */
@Getter
@Setter
@ApiModel(description = "巡检工单查询条件")
public class InspectionSearchDto {
    @ApiModelProperty("开始时间")
    @NotNull(message = "开始时间不允许为空！")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    @NotNull(message = "结束时间不允许为空！")
    private LocalDateTime endTime;

    @ApiModelProperty("工单号，模糊匹配")
    private String code;

    @ApiModelProperty("班组")
    private Long teamId;

    @ApiModelProperty("异常原因")
    private List<Integer> abnormalTypes;

    @ApiModelProperty("工单状态")
    private Integer workSheetStatus;

    @ApiModelProperty("工单类型")
    private Integer taskType = WorkSheetTaskType.INSPECTION;

    @ApiModelProperty("分页")
    private Page page;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("签到点id")
    private Long signPointId;

    @ApiModelProperty("签到点分组id")
    private Long signGroupId;

    private List<String> selectFields;
}


