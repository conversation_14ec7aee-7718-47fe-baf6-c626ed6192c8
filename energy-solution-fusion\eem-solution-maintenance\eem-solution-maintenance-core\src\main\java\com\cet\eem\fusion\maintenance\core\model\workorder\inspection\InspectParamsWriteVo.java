﻿package com.cet.eem.fusion.maintenance.core.model.workorder.inspection;

import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.Attachment;
import com.cet.eem.fusion.maintenance.core.model.workorder.OperationUser;
import com.cet.eem.fusion.maintenance.core.model.workorder.repair.RepairWorkOrderAddDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/7
 */
@Getter
@Setter
@ApiModel("填写巡检工单参数")
public class InspectParamsWriteVo {
    private Long id;

    private String code;

    private Object taskObject;

    @ApiModelProperty("巡检参数")
    private List<InspectParams> params;

    @ApiModelProperty("操作用户信息")
    private List<OperationUser> users;

    @ApiModelProperty("实际开始时间")
    @JsonProperty(WorkOrderDef.EXECUTE_TIME)
    private LocalDateTime executeTime;

    @ApiModelProperty("工单附件")
    private List<Attachment> attachment;

    @ApiModelProperty("处理描述")
    @JsonProperty(WorkOrderDef.HANDLE_DESCRIPTION)
    private String handleDescription;

    @ApiModelProperty("是否异常，异常为true，否则为false")
    private boolean abnormal;

    @ApiModelProperty("结束时间")
    private LocalDateTime finishTime;

    @ApiModelProperty("关联工单")
    @JsonProperty(WorkOrderDef.RELATED_CODE)
    private String relatedCode;

    @ApiModelProperty("关联工单id")
    @JsonProperty(WorkOrderDef.RELATED_CODE_ID)
    private Long relatedCodeId;

    @ApiModelProperty("创建维修工单数据")
    private RepairWorkOrderAddDto repairWorkOrderAddDto;
}

