﻿package com.cet.eem.fusion.maintenance.core.controller.bff.inspect;

import com.cet.eem.bll.common.log.annotation.OperationLog;
import com.cet.eem.bll.common.log.constant.EEMOperationLogType;
import com.cet.eem.bll.common.log.constant.EnumOperationSubType;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.InspectionParameter;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.InspectionScheme;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.InspectionSchemeDetail;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.InspectionSchemeDetailVo;
import com.cet.eem.fusion.maintenance.core.model.param.AddInspectionParameterRequest;
import com.cet.eem.fusion.maintenance.core.model.param.QueryInspectionParameterByDevice;
import com.cet.eem.fusion.maintenance.core.model.param.QueryInspectionParameterRequest;
import com.cet.eem.fusion.maintenance.core.model.param.UpdateInspectionParameterRequest;
import com.cet.eem.fusion.maintenance.core.service.inspection.InspectionParameterService;
import com.cet.electric.commons.ApiResult;

import com.cet.electric.commons.ApiResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName : InspectionParameterController
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-14 14:19
 */

public class InspectionParameterBffController {

    @Autowired
    private InspectionParameterService inspectionParameterService;

    @ApiOperation(value = "新增巡检参数")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR_PARAM, subType = EnumOperationSubType.ADD, description = "【新增巡检参数】")
    @PostMapping
    public ApiResult<InspectionParameter> addInspectionParameter(@Valid @RequestBody AddInspectionParameterRequest addInspectionParameterRequest) {
        return ApiResult.ok(inspectionParameterService.addInspectionParameter(addInspectionParameterRequest));
    }

    @ApiOperation(value = "查询当前项目巡检参数")
    @PostMapping("/query")
    public ApiResult<List<InspectionParameter>> queryAllInspectionParameter(@Valid @RequestBody QueryInspectionParameterRequest queryInspectionParameterRequest) {
        return inspectionParameterService.queryAllInspectionParameter(queryInspectionParameterRequest);
    }

    /**
     * 删除巡检参数
     *
     * @param ids
     */
    @ApiOperation(value = "删除巡检参数")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR_SCHEME, subType = EnumOperationSubType.DELETE, description = "【删除巡检参数】")
    @DeleteMapping
    public ApiResult<Void> deleteInspectionParameter(@RequestBody List<Long> ids) {
        inspectionParameterService.deleteInspectionParameter(ids);
        return ApiResult.ok();
    }

    /**
     * 更新巡检参数
     *
     * @param updateInspectionParameterRequest
     * @return
     */
    @ApiOperation(value = "编辑巡检参数")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR_SCHEME, subType = EnumOperationSubType.UPDATE, description = "【更新巡检参数】")
    @PatchMapping
    public ApiResult<InspectionParameter> updateInspectionParameter(@Valid @RequestBody UpdateInspectionParameterRequest updateInspectionParameterRequest) {
        return ApiResult.ok(inspectionParameterService.updateInspectionParameter(updateInspectionParameterRequest));
    }

    @ApiOperation(value = "根据设备查询巡检参数")
    @PostMapping("/queryByDevice")
    public ApiResult<List<InspectionSchemeDetailVo>> queryInspectionParameterByDevice(@RequestBody @Validated QueryInspectionParameterByDevice queryInspectionParameterByDevice) {
        List<InspectionSchemeDetailVo> inspectionSchemeDetailVos = inspectionParameterService.queryInspectionParameterByDevice(queryInspectionParameterByDevice);
        return ApiResult.ok(inspectionSchemeDetailVos);
    }

    @ApiOperation(value = "根据设备查询巡检方案")
    @PostMapping("/querySchemeByDevice")
    public ApiResult<List<InspectionScheme>> queryInspectionSchemeByDevice(@RequestBody @Validated QueryInspectionParameterByDevice queryInspectionParameterByDevice) {
        List<InspectionScheme> inspectionSchemes = inspectionParameterService.queryInspectionSchemeByDevice(queryInspectionParameterByDevice);
        return ApiResult.ok(inspectionSchemes);
    }

    @ApiOperation(value = "根据巡检方案查询巡检参数")
    @GetMapping("/queryDetailByScheme")
    public ApiResult<List<InspectionSchemeDetail>> queryDetailByScheme(@RequestParam Long inspectionSchemeId,@RequestParam Integer inspectionParameterType) {
        List<InspectionSchemeDetail> inspectionSchemeDetails = inspectionParameterService.queryDetailByScheme(inspectionSchemeId, inspectionParameterType);
        return ApiResult.ok(inspectionSchemeDetails);
    }
}


