﻿package com.cet.eem.fusion.maintenance.core.model.workorder.inspection.recordsheet;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : PatrolFavorites
 * @Description : 收藏文件夹
 * <AUTHOR> jiangzixuan
 * @Date: 2022-10-10 11:16
 */
@Getter
@Setter
@ModelLabel(RecordSheetModelLabel.PATROL_FAVORITES)
public class PatrolFavorites extends EntityWithName {
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;

    public PatrolFavorites() {
        this.modelLabel = RecordSheetModelLabel.PATROL_FAVORITES;
    }
}

