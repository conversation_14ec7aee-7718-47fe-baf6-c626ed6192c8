# Supplementary Fusion Adaptation Script
Write-Host "Running supplementary fusion adaptation fixes..." -ForegroundColor Green

$coreSourcePath = "energy-solution-fusion\eem-solution-maintenance\eem-solution-maintenance-core\src\main\java"

# Additional import additions needed
$additionalImports = @{
    "NumberCalcUtils.calcDouble" = "import com.cet.eem.fusion.common.utils.datatype.NumberCalcUtils;"
    "ContentTypeDef.APPLICATION_MSEXCEL" = "import com.cet.eem.fusion.common.def.common.ContentTypeDef;"
    "ContentTypeDef.APPLICATION_MS_EXCEL_07" = "import com.cet.eem.fusion.common.def.common.ContentTypeDef;"
    "ErrorCode.SUCCESS_CODE" = "import com.cet.eem.fusion.common.def.common.ErrorCode;"
    "StringFormatUtils.BLANK_STR" = "import com.cet.eem.fusion.common.utils.datatype.StringFormatUtils;"
    "StringFormatUtils.formatDoubleWithOutScientificNotation" = "import com.cet.eem.fusion.common.utils.datatype.StringFormatUtils;"
}

function Add-MissingImports {
    param(
        [string]$filePath
    )
    
    if (!(Test-Path $filePath)) {
        return
    }
    
    $content = Get-Content $filePath -Raw -Encoding UTF8
    $originalContent = $content
    $changed = $false
    
    # Check if file needs additional imports
    foreach ($usage in $additionalImports.Keys) {
        $importStatement = $additionalImports[$usage]
        
        # If the usage exists in the file but import doesn't exist
        if ($content -match [regex]::Escape($usage) -and $content -notmatch [regex]::Escape($importStatement)) {
            # Find the last import statement and add the new import after it
            $importPattern = '(?m)^import\s+[^;]+;'
            $matches = [regex]::Matches($content, $importPattern)
            
            if ($matches.Count -gt 0) {
                $lastImport = $matches[$matches.Count - 1]
                $insertPosition = $lastImport.Index + $lastImport.Length
                $content = $content.Insert($insertPosition, "`n$importStatement")
                $changed = $true
                Write-Host "  - Added missing import: $importStatement" -ForegroundColor Yellow
            }
        }
    }
    
    if ($changed) {
        Set-Content $filePath -Value $content -Encoding UTF8
        Write-Host "✓ Added missing imports to: $filePath" -ForegroundColor Green
    }
}

# Process all Java files to add missing imports
Write-Host "Adding missing imports..." -ForegroundColor Cyan
$javaFiles = Get-ChildItem -Path $coreSourcePath -Filter "*.java" -Recurse

foreach ($file in $javaFiles) {
    Add-MissingImports -filePath $file.FullName
}

Write-Host "`nSupplementary adaptation completed!" -ForegroundColor Green
