﻿package com.cet.eem.fusion.maintenance.core.dao.maintenance;

import com.cet.eem.annotation.ModelDao;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.MaintenanceItem;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;
import com.cet.eem.model.base.ConditionBlock;
import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import io.micrometer.core.instrument.Meter;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.constraints.NotNull;
import java.util.*;

/**
 * @ClassName : MaintenanceGroupDao
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-23 13:46
 */
public interface MaintenanceItemDao extends BaseModelDao<MaintenanceItem> {

    /**
     * 根据计划查询关联的维保项目
     *
     * @param planSheetId
     * @return
     */
    List<MaintenanceItem> queryMaintenanceByPlanSheet(Long planSheetId);

    /**
     * 查询维保项以及关联的计划
     *
     * @param itemIds 维保项id
     * @return 维保项
     */
    List<Map<String, Object>> queryMaintenanceWithPlanSheet(@NotNull Collection<Long> itemIds);

    /**
     * 根据维保类型查询维保项目
     *
     * @param types
     * @param page
     * @return
     */
    ApiResult<List<MaintenanceItem>> queryMaintenanceByTypes(List<Long> types, Page page);

}


