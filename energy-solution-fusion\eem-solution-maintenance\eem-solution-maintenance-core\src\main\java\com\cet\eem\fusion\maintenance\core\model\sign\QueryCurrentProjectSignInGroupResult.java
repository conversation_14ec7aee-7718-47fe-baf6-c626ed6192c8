﻿package com.cet.eem.fusion.maintenance.core.model.sign;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInGroup;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.SignInPointWithSupLayer;
import com.cet.eem.common.definition.ModelLabelDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : QueryCurrentProjectSignInGroupResult
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-08 14:29
 */
@Getter
@Setter
public class QueryCurrentProjectSignInGroupResult extends SignInGroup {

    @JsonProperty(ModelLabelDef.REGISTRATION_POINT + "_model")
    private List<SignInPointWithSupLayer> signInPointList;
}

