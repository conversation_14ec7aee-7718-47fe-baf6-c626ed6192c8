﻿package com.cet.eem.fusion.maintenance.core.schedule.strategy;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;
import org.quartz.CalendarIntervalScheduleBuilder;
import org.quartz.CalendarIntervalTrigger;
import org.quartz.ScheduleBuilder;
import org.springframework.stereotype.Component;

/**
 * @ClassName : OneDayTriggerStrategy
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-26 10:37
 */
@Component(PlanSheetTriggerStrategyKey.ONE_YEAR)
public class OneYearTriggerStrategy implements PlanSheetTriggerStrategy<CalendarIntervalTrigger> {

    @Override
    public ScheduleBuilder<CalendarIntervalTrigger> buildSchedule(PlanSheet planSheet) {
        return CalendarIntervalScheduleBuilder
                .calendarIntervalSchedule()
                .withIntervalInYears(1)
                .withMisfireHandlingInstructionDoNothing();
    }
}

