﻿package com.cet.eem.fusion.maintenance.core.model.devicemanage.template;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-04-26
 */
@Data
@AllArgsConstructor
@ApiModel("模板节点树表")
@ModelLabel(ModelLabelDef.TEMPLATE_NODE_TREE)
public class EquipmentNodeTreeDto extends EntityWithName {

    @ApiModelProperty("节点名称")
    private String name;

    @ApiModelProperty("父节点id")
    @JsonProperty("parentid")
    private Long parentId;

    @ApiModelProperty("项目id")
    @JsonProperty("projectid")
    private Long projectId;

    public EquipmentNodeTreeDto() {
        this.modelLabel = ModelLabelDef.TEMPLATE_NODE_TREE;
    }
}


