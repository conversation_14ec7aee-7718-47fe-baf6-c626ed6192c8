﻿package com.cet.eem.fusion.maintenance.core.model.workorder.inspection.recordsheet;

import com.cet.eem.fusion.maintenance.core.model.devicemanage.TechParamValue;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * @ClassName : NodeParamInfo
 * @Description : 设备信息加技术参数信息
 * <AUTHOR> jiang<PERSON>xuan
 * @Date: 2022-11-04 10:49
 */
@Getter
@Setter
public class NodeParamInfo {
   private List<Map<String,Object>> nodeData;
   private List<TechParamValue> techParamValues;
}
