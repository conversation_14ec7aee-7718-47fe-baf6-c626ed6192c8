﻿package com.cet.eem.fusion.maintenance.core.schedule.util;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;
import com.cet.eem.fusion.maintenance.core.schedule.job.PlanSheetJob;
import com.cet.eem.fusion.maintenance.core.schedule.strategy.PlanSheetTriggerStrategy;
import com.cet.eem.fusion.maintenance.core.schedule.strategy.PlanSheetTriggerStrategyKey;
import com.cet.eem.toolkit.Assert;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName : SheetPlanConverter
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-23 09:55
 */
@Component
public class PlanSheetConverter {

    @Autowired
    private Map<String, PlanSheetTriggerStrategy<? extends Trigger>> contextMap;

    /**
     * 巡检计划转换为job实例
     *
     * @return
     */
    public JobDetail sheetPlanConvertToJobInstance(PlanSheet planSheet) {
        //jobDataMap 用于存放自定义数据，传递到JobExecutionContext中
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put(planSheet.getModelLabel(), planSheet.getId());
        //因为一个计划只会有一个定时任务,所以选择用id作为唯一key
        return JobBuilder.newJob(PlanSheetJob.class)
                .withIdentity(planSheet.getId().toString())
                .setJobData(jobDataMap).build();
    }

    @SuppressWarnings("unchecked")
    public Trigger sheetPlanConvertToTrigger(PlanSheet planSheet) {
        ScheduleBuilder scheduleBuilder = getPlanSheetTriggerStrategy(planSheet.getAggregationCycle()).buildSchedule(planSheet);
        TriggerBuilder<Trigger> triggerBuilder = TriggerBuilder.newTrigger()
                .withIdentity(planSheet.getId().toString(), planSheet.getModelLabel())
                .withSchedule(scheduleBuilder);
        Long realStartTime = PlanTimeUtils.getRealStartTime(planSheet);
        triggerBuilder.startAt(new Date(realStartTime));
        if (Objects.nonNull(planSheet.getFinishTime())) {
            triggerBuilder.endAt(new Date(planSheet.getFinishTime()));
        }
        return triggerBuilder.build();
    }

    private PlanSheetTriggerStrategy<? extends Trigger> getPlanSheetTriggerStrategy(Integer aggregationCycle) {
        String key = PlanSheetTriggerStrategyKey.generatorStrategyKey(aggregationCycle);
        PlanSheetTriggerStrategy<? extends Trigger> planSheetTriggerStrategy = contextMap.get(key);
        Assert.notNull(planSheetTriggerStrategy, String.format("找不到周期为%s的定时策略", aggregationCycle));
        return planSheetTriggerStrategy;
    }
}

