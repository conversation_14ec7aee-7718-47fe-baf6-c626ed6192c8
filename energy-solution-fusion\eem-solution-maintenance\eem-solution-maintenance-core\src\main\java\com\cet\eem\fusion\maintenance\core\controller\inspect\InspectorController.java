﻿package com.cet.eem.fusion.maintenance.core.controller.inspect;

import com.cet.eem.fusion.maintenance.core.controller.bff.inspect.InspectorBffController;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName : InspectorController
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-02 10:08
 */
@Api(value = "/eem/v1/inspector", tags = "巡检人员管理")
@RequestMapping(value = "/eem/solution/maintenance/eem/v1/inspector")
@RestController
@Validated
public class InspectorController extends InspectorBffController {

}


