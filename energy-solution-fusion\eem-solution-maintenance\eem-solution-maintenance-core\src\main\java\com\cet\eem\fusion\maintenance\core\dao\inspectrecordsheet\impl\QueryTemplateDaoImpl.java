﻿package com.cet.eem.fusion.maintenance.core.dao.inspectrecordsheet.impl;

import com.cet.eem.fusion.maintenance.core.dao.inspectrecordsheet.QueryTemplateDao;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.recordsheet.QueryTemplate;
import com.cet.eem.fusion.maintenance.core.model.workorder.inspection.recordsheet.QueryTemplateWithLayer;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : QueryTemplateDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-10-13 16:28
 */
@Repository
public class QueryTemplateDaoImpl extends ModelDaoImpl<QueryTemplate> implements QueryTemplateDao {

    @Override
    public List<QueryTemplateWithLayer> queryTemplate(Long projectId) {
        LambdaQueryWrapper<QueryTemplate> queryWrapper = LambdaQueryWrapper.of(QueryTemplate.class)
                .eq(QueryTemplate::getProjectId, projectId);
        List<QueryTemplate> queryTemplates = this.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(queryTemplates)){
            return Collections.emptyList();
        }
        Set<Long> ids = queryTemplates.stream().filter(queryTemplate -> Objects.isNull(queryTemplate.getDetail()))
                .map(QueryTemplate::getId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(ids)){
            return transDataType(queryTemplates);
        }
        queryWrapper.in(QueryTemplate::getId, ids);
        List<QueryTemplateWithLayer> queryTemplateWithLayers = this.selectRelatedList(QueryTemplateWithLayer.class, queryWrapper);
        if (CollectionUtils.isEmpty(queryTemplateWithLayers)) {
            return Collections.emptyList();
        }
        return queryTemplateWithLayers;
    }
    private List<QueryTemplateWithLayer> transDataType(List<QueryTemplate> queryTemplates){
        List<QueryTemplateWithLayer> result=new ArrayList<>();
        for (QueryTemplate template:queryTemplates){
            QueryTemplateWithLayer layer=new QueryTemplateWithLayer();
            BeanUtils.copyProperties(template,layer);
            result.add(layer);
        }
        return result;
    }
    @Override
    public List<QueryTemplate> querySameNameTemplate(Long id, String name) {
        LambdaQueryWrapper<QueryTemplate> queryWrapper = LambdaQueryWrapper.of(QueryTemplate.class)
                .eq(QueryTemplate::getName, name);
        if (Objects.nonNull(id)){
            queryWrapper.ne(QueryTemplate::getId, id);
        }
        return this.selectList(queryWrapper);
    }

    @Override
    public List<QueryTemplate> queryChildrenData(Long parentId, Long id, String name) {
        LambdaQueryWrapper<QueryTemplate> queryWrapper = LambdaQueryWrapper.of(QueryTemplate.class)
                .eq(QueryTemplate::getName, name);
        if (Objects.nonNull(id)){
            queryWrapper.ne(QueryTemplate::getId, id);
        }
        QueryTemplateWithLayer queryTemplateWithLayer = this.selectRelatedById(QueryTemplateWithLayer.class, parentId, Collections.singletonList(queryWrapper));
        if (Objects.isNull(queryTemplateWithLayer)) {
            return Collections.emptyList();
        }
        return queryTemplateWithLayer.getQueryTemplates();
    }

    @Override
    public List<QueryTemplateWithLayer> queryTemplate(Collection<Long> templateIds) {
        LambdaQueryWrapper<QueryTemplate> queryWrapper = LambdaQueryWrapper.of(QueryTemplate.class)
                .in(QueryTemplate::getId, templateIds);
        List<QueryTemplateWithLayer> queryTemplateWithLayers = this.selectRelatedList(QueryTemplateWithLayer.class, null,Collections.singletonList(queryWrapper));
        if (CollectionUtils.isEmpty(queryTemplateWithLayers)) {
            return Collections.emptyList();
        }
        return queryTemplateWithLayers;
    }
}

